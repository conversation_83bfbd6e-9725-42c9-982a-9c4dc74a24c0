/* Component styles for the Euranet Dashboard */

/* Message Styling */
.message {
    margin-bottom: 1rem;
    padding: 0.8rem 1.1rem; /* Slightly adjust padding */
    border-radius: 0.75rem; /* Softer corners */
    max-width: 85%; /* Slightly wider */
    position: relative; /* For timestamp positioning */
    word-wrap: break-word; /* Ensure long words break */
    transition: background-color var(--transition-speed), color var(--transition-speed);
    animation: fadeInSlideUp 0.4s ease-out forwards; /* Apply animation */
}

/* Theme-specific message styling */
.app-container.dark-mode .assistant-message {
    background-color: var(--card-bg);
    color: var(--light-text);
    align-self: flex-start; /* Align bot messages left */
}
.app-container.dark-mode .user-message {
    background-color: var(--primary-color);
    color: var(--light-text); /* Ensure text is light on primary bg */
    align-self: flex-end !important; /* Re-add !important */
}

.app-container.light-mode .assistant-message {
    background-color: var(--light-card-bg);
    color: var(--dark-text);
    border: 1px solid var(--light-border-color); /* Add subtle border */
    align-self: flex-start;
}
.app-container.light-mode .user-message {
    background-color: var(--light-primary-color);
    color: var(--light-text); /* Ensure text is light on primary bg */
    align-self: flex-end !important; /* Re-add !important */
}

.message-content { /* Style the inner content div */
     line-height: 1.5;
     padding-bottom: 1.5rem; /* Space for timestamp */
}

.message-timestamp { /* Position timestamp */
    font-size: 0.7rem;
    opacity: 0.7;
    position: absolute;
    bottom: 0.5rem;
    right: 0.75rem;
}

/* Chat Input Area */
.chat-input-area { /* Renamed from .chat-input */
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color); /* Default border */
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Space between input and button */
    transition: border-color var(--transition-speed);
}
/* Theme-specific border */
.app-container.dark-mode .chat-input-area {
    border-top-color: var(--border-color);
}
.app-container.light-mode .chat-input-area {
    border-top-color: var(--light-border-color);
}

/* Input Field Styling (using dbc class .chat-input) */
.chat-input {
    flex: 1;
    border-radius: 1.5rem !important; /* Use !important if needed */
    border: 1px solid var(--border-color) !important;
    padding: 0.6rem 1rem !important;
    resize: none;
    min-height: 40px !important; /* Fixed height */
    line-height: 1.4;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
/* Theme-specific input field */
.app-container.dark-mode .chat-input {
    background-color: var(--card-bg) !important;
    color: var(--light-text) !important;
    border-color: var(--border-color) !important;
}
.app-container.light-mode .chat-input {
    background-color: var(--light-card-bg) !important;
    color: var(--dark-text) !important;
    border-color: var(--light-border-color) !important;
}
.app-container.dark-mode .chat-input:focus {
    outline: none !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(98, 198, 217, 0.3); /* Subtle focus glow */
}
.app-container.light-mode .chat-input:focus {
    outline: none !important;
    border-color: var(--light-primary-color) !important;
    box-shadow: 0 0 0 2px rgba(90, 155, 213, 0.3); /* Subtle focus glow */
}

/* Send Button Styling (using dbc class .send-button) */
.send-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important; /* Remove default padding */
    transition: background-color var(--transition-speed), color var(--transition-speed);
}
/* Theme-specific send button */
.app-container.dark-mode .send-button {
    background-color: var(--primary-color) !important;
    color: var(--light-text) !important;
    border: none !important;
}
.app-container.dark-mode .send-button:hover {
    background-color: var(--secondary-color) !important;
}
.app-container.light-mode .send-button {
    background-color: var(--light-primary-color) !important;
    color: var(--light-text) !important;
    border: none !important;
}
.app-container.light-mode .send-button:hover {
    background-color: var(--light-secondary-color) !important;
}
.send-button i { /* Style the icon inside */
    font-size: 1rem;
}

/* Loading Indicator */
.loading-indicator-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem 1rem 0; /* Padding top only */
    height: 30px; /* Fixed height to prevent layout shifts */
}
.loading-spinner {
    opacity: 1; /* Increased opacity for better visibility */
    transform: scale(1.2); /* Make spinner slightly larger */
}

.loading-spinner > div {
    animation: pulse 1.5s infinite ease-in-out; /* Apply pulsing animation */
}

/* Theme Toggle Button */
.theme-toggle-button {
    border-radius: 50% !important; /* Ensure it's round */
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
/* Dark mode button style */
.app-container.dark-mode .theme-toggle-button {
    color: var(--light-text);
    border-color: var(--secondary-color);
}
.app-container.dark-mode .theme-toggle-button:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}
/* Light mode button style */
.app-container.light-mode .theme-toggle-button {
    color: var(--dark-text);
    border-color: var(--light-secondary-color);
}
.app-container.light-mode .theme-toggle-button:hover {
    background-color: var(--light-secondary-color);
    color: var(--light-text); /* White icon on hover */
}

/* Suggestions Area */
.suggestion-section { /* Renamed from .suggestions-container */
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color); /* Default border */
    transition: border-color var(--transition-speed);
}
.app-container.dark-mode .suggestion-section {
    border-top-color: var(--border-color);
}
.app-container.light-mode .suggestion-section {
    border-top-color: var(--light-border-color);
}

.suggestion-title { /* Renamed from .suggestions-title */
    font-size: 0.8rem;
    font-weight: 500; /* Slightly bolder */
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    opacity: 0.8; /* Slightly less prominent */
}

/* Suggestions List (using dbc class .suggestion-pill) */
.suggestion-section .d-flex { /* Target the flex container dbc creates */
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-pill { /* Renamed from .suggestion-button */
    border-radius: 1rem !important;
    padding: 0.35rem 0.9rem !important; /* Fine-tune padding */
    font-size: 0.78rem !important; /* Slightly larger font */
    font-weight: 500 !important; /* Medium weight */
    cursor: pointer;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed), transform 0.1s ease-out; /* Add transform transition */
    line-height: 1.4; /* Ensure consistent line height */
    border-width: 1px !important; /* Ensure border width is consistent */
    border-style: solid !important;
}
/* Theme-specific suggestion pills */
.app-container.dark-mode .suggestion-pill {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--light-text) !important;
}
.app-container.dark-mode .suggestion-pill:hover {
    background-color: var(--hover-color) !important;
    border-color: var(--hover-color) !important;
    transform: translateY(-1px); /* Slight lift on hover */
}
.app-container.light-mode .suggestion-pill {
    background-color: var(--light-card-bg) !important;
    border: 1px solid var(--light-border-color) !important;
    color: var(--dark-text) !important;
}
.app-container.light-mode .suggestion-pill:hover {
    background-color: var(--light-hover-color) !important;
    border-color: var(--light-hover-color) !important;
    transform: translateY(-1px); /* Slight lift on hover */
}

/* Fixed Key Insights Section at the bottom */
.key-insights-fixed-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10; /* Ensure it's above other content */
    padding: 1rem 1.5rem; /* Match card padding */
    margin: 0; /* No margin needed */
    border-top: 4px solid var(--primary-color); /* Thicker border for better visibility */
    box-shadow: 0 -6px 12px rgba(0, 0, 0, 0.25); /* Stronger shadow for better separation */
    background-color: var(--card-bg); /* Match card background */
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
    /* Add a clear visual separator */
    position: relative; /* For the separator */
}
/* Add a clear visual separator line before the key insights section */
.key-insights-fixed-section::before {
    content: '';
    position: absolute;
    top: -1px; /* Position it at the top of the section */
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--border-color); /* Use border color for the line */
    z-index: 11; /* Above the section itself */
}

/* Theme-specific styling for key insights section */
.app-container.dark-mode .key-insights-fixed-section {
    background-color: var(--dark-bg); /* Use darker background for better contrast */
    border-top-color: var(--primary-color);
}
.app-container.light-mode .key-insights-fixed-section {
    background-color: var(--light-bg); /* Use lighter background for better contrast */
    border-top-color: var(--light-primary-color);
}
.app-container.dark-mode .key-insights-fixed-section::before {
    background-color: var(--border-color);
}
.app-container.light-mode .key-insights-fixed-section::before {
    background-color: var(--light-border-color);
}

.key-insights-title {
    font-size: 1.1rem; /* Slightly larger */
    font-weight: 600;
    margin-bottom: 0.75rem; /* Reduced margin for fixed section */
    transition: color var(--transition-speed);
    display: flex;
    align-items: center;
}

/* Style for the key insights content in the fixed section */
.key-insights-content {
    max-height: 120px; /* Allow some space for scrolling */
    overflow-y: auto; /* Enable scrolling if content is too long */
}

/* Style for the horizontal separator in key insights */
.key-insights-separator {
    border: none;
    height: 2px;
    background-color: var(--border-color);
    margin: 0 0 0.75rem 0;
    opacity: 0.7;
}

/* Theme-specific styling for the separator */
.app-container.dark-mode .key-insights-separator {
    background-color: var(--border-color);
}
.app-container.light-mode .key-insights-separator {
    background-color: var(--light-border-color);
}
.app-container.dark-mode .key-insights-title {
    color: var(--light-text);
}
.app-container.light-mode .key-insights-title {
    color: var(--dark-text);
}

.insight-item {
    margin-bottom: 0.5rem; /* Reduced margin for fixed section */
    display: flex;
    align-items: flex-start;
    font-size: 0.85rem; /* Slightly smaller for fixed section */
}

.insight-icon {
    margin-right: 0.75rem; /* Increased spacing */
    margin-top: 0.15rem; /* Align icon better */
    transition: color var(--transition-speed);
}
.app-container.dark-mode .insight-icon {
    color: var(--primary-color);
}
.app-container.light-mode .insight-icon {
    color: var(--light-primary-color);
}

.insight-text {
    line-height: 1.6; /* Improved readability */
}

/* Empty State Styling */
.empty-state-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    padding: 2rem;
    border-radius: 8px;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}
.app-container.dark-mode .empty-state-container {
    background-color: var(--card-bg);
    border: 1px dashed var(--border-color);
}
.app-container.light-mode .empty-state-container {
    background-color: var(--light-card-bg);
    border: 1px dashed var(--light-border-color);
}
.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}
.empty-state-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: color var(--transition-speed);
}
.app-container.dark-mode .empty-state-title {
    color: var(--primary-color);
}
.app-container.light-mode .empty-state-title {
    color: var(--light-primary-color);
}
.empty-state-text {
    opacity: 0.7;
    font-size: 0.9rem;
}
