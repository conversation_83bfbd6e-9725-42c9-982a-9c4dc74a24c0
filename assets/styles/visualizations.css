/* Visualization component styles for the Euranet Dashboard */

/* Card Styling for Visualization Panel Sections */
.chart-container,
.data-table-section,
.key-insights-section,
.query-info {
    border-radius: 8px;
    padding: 1.75rem;  /* Increased padding */
    margin-bottom: 2rem;  /* Increased margin */
    transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
    box-shadow: 0 2px 4px var(--shadow-color);
}
.app-container.dark-mode .chart-container,
.app-container.dark-mode .data-table-section,
.app-container.dark-mode .key-insights-section,
.app-container.dark-mode .query-info {
    background-color: var(--card-bg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow in dark mode */
}
.app-container.light-mode .chart-container,
.app-container.light-mode .data-table-section,
.app-container.light-mode .key-insights-section,
.app-container.light-mode .query-info {
    background-color: var(--light-card-bg);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Lighter shadow in light mode */
}

/* Current Query Section */
.current-query {
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    background-color: var(--card-bg);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.chart-container {
    margin-top: 1rem;  /* Add top margin */
    margin-bottom: 2rem; /* Add extra margin to ensure space between chart and other elements */
}

/* Chart Title - Consolidated and Adjusted */
.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    transition: color var(--transition-speed);
}
.app-container.dark-mode .chart-title {
    color: var(--light-text);
}
.app-container.light-mode .chart-title {
    color: var(--dark-text);
}

.visualization-subtitle {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Add spacing between chart and insights */
.visualization-container {
    padding-bottom: calc(2rem + 180px);  /* Increased padding */
}

.data-table-section {
    position: relative;
    margin-bottom: 3rem;  /* Increased margin for better separation */
    padding: 1.75rem;
}

/* Add visual separator between sections */
.data-table-section::after {
    content: '';
    position: absolute;
    bottom: -1.5rem;
    left: 1.75rem;
    right: 1.75rem;
    height: 1px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    opacity: 0.3;
}

.app-container.light-mode .data-table-section::after {
    background: linear-gradient(90deg, var(--light-primary-color), transparent);
}

.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem; /* Increased margin */
}

.data-table-title {
    font-size: 1.1rem; /* Slightly larger */
    font-weight: 600;
    margin: 0;
    transition: color var(--transition-speed);
}
.app-container.dark-mode .data-table-title {
    color: var(--light-text);
}
.app-container.light-mode .data-table-title {
    color: var(--dark-text);
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.02);  /* Subtle background */
}

.app-container.light-mode .data-table {
    background: rgba(0, 0, 0, 0.02);
}

/* Theme-specific table borders and header */
.app-container.dark-mode .data-table th,
.app-container.dark-mode .data-table td {
    border-color: var(--border-color);
}
.app-container.light-mode .data-table th,
.app-container.light-mode .data-table td {
    border-color: var(--light-border-color);
}
.app-container.dark-mode .data-table th {
    background-color: rgba(255, 255, 255, 0.05); /* Darker header */
    color: var(--light-text); /* Ensure header text is light */
}
.app-container.light-mode .data-table th {
    background-color: rgba(0, 0, 0, 0.03); /* Lighter header */
    color: var(--dark-text); /* Ensure header text is dark */
}
/* Ensure table body and cells inherit correct background/color */
.app-container.dark-mode .data-table tbody,
.app-container.dark-mode .data-table td {
    background-color: transparent !important; /* Ensure transparency */
    color: var(--light-text) !important;
}
.app-container.light-mode .data-table tbody,
.app-container.light-mode .data-table td {
    background-color: transparent !important; /* Ensure transparency */
    color: var(--dark-text) !important;
}

/* Table header styling */
.data-table th {
    text-align: left;
    padding: 1rem;
    font-weight: 600;
    font-size: 0.9rem; /* Slightly smaller header font */
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;  /* Prevent header text wrapping */
}

/* Table cell styling */
.data-table td {
    padding: 0.875rem 1rem;
    font-size: 0.85rem; /* Slightly smaller cell font */
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Add theme-specific hover effect for table rows */
.app-container.dark-mode .data-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.08) !important; /* Slightly lighter hover */
    color: var(--light-text);
}
.app-container.light-mode .data-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05) !important; /* Slightly darker hover */
    color: var(--dark-text);
}

/* Row hover effect */
.data-table tr:hover td {
    background-color: rgba(255, 255, 255, 0.03);
}

.app-container.light-mode .data-table tr:hover td {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Hide potential stray button near data table */
.data-table-section > button {
    display: none !important;
}

/* Query Info Styling */
.query-info {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem; /* Adjusted padding */
    border-radius: 0.5rem;
    border-left-width: 4px;
    border-left-style: solid;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}
.app-container.dark-mode .query-info {
    background-color: var(--card-bg);
    border-left-color: var(--primary-color);
}
.app-container.light-mode .query-info {
    background-color: var(--light-card-bg);
    border-left-color: var(--light-primary-color);
}

.subtitle { /* Used in query-info and empty-state */
    font-size: 0.8rem;
    text-transform: uppercase;
    opacity: 0.7;
    margin-bottom: 0.25rem;
    font-weight: 500; /* Slightly bolder */
}

.query-text {
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 0;
    font-style: italic; /* Italicize the query */
}

/* Technical Details Accordion */
.tech-details-accordion-container {
    margin-top: 1.5rem;
}
.tech-details-accordion .accordion-button {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}
.tech-details-accordion .accordion-body {
    font-size: 0.8rem;
    padding: 1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
.tech-details-accordion .accordion-body code { /* Style code blocks */
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
}

/* Theme-specific accordion */
.app-container.dark-mode .tech-details-accordion .accordion-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-button {
    background-color: var(--card-bg);
    color: var(--light-text);
}
.app-container.dark-mode .tech-details-accordion .accordion-button:not(.collapsed) {
    background-color: var(--hover-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-body {
    background-color: var(--dark-bg); /* Slightly darker body */
    color: var(--light-text);
    border-top: 1px solid var(--border-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-body code {
    background-color: rgba(255, 255, 255, 0.1);
    color: #cdd5f5; /* Light blueish code text */
}

.app-container.light-mode .tech-details-accordion .accordion-item {
    background-color: var(--light-card-bg);
    border-color: var(--light-border-color);
}
.app-container.light-mode .tech-details-accordion .accordion-button {
    background-color: var(--light-card-bg);
    color: var(--dark-text);
}
.app-container.light-mode .tech-details-accordion .accordion-button:not(.collapsed) {
    background-color: var(--light-hover-color);
}
.app-container.light-mode .tech-details-accordion .accordion-body {
    background-color: #f8f9fa; /* Slightly off-white body */
    color: var(--dark-text);
    border-top: 1px solid var(--light-border-color);
}
.app-container.light-mode .tech-details-accordion .accordion-body code {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333;
}

/* Plotly Chart Fixes */
/* Apply !important cautiously, only where necessary to override Plotly defaults */
.js-plotly-plot .plotly .xtick text,
div[class*="plotly"] .xtick text,
.dash-graph .xtick text {
    transform: rotate(-45deg) !important; /* Angle labels for better fit */
    text-anchor: end !important; /* Align text end for angled labels */
    font-size: 10px !important; /* Smaller font size */
    font-family: 'Inter', Arial, sans-serif !important;
    transform-origin: bottom right !important; /* Adjust rotation origin */
}

/* Chart container styling */
/* Apply margin to the container, not the plot itself */
.chart-container .js-plotly-plot,
.chart-container div[class*="plotly"],
.chart-container .dash-graph {
    margin-bottom: 40px !important; /* Reduced margin */
    height: 450px !important; /* Reduced height to eliminate extra space */
    padding-bottom: 0 !important; /* Remove padding that creates extra space */
}

/* Ensure the SVG inside the chart maintains aspect ratio */
.chart-container .js-plotly-plot .plot-container .svg-container {
    height: 450px !important; /* Reduced height to match container */
    margin: 0 auto;  /* Center the chart */
}

/* Fix axis labels */
.js-plotly-plot .plotly .ytitle,
div[class*="plotly"] .ytitle,
.dash-graph .ytitle {
    font-family: 'Inter', Arial, sans-serif !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    color: var(--light-text) !important; /* Ensure visibility in dark mode */
}

.js-plotly-plot .plotly .xtitle,
div[class*="plotly"] .xtitle,
.dash-graph .xtitle {
    font-family: 'Inter', Arial, sans-serif !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 20px !important; /* Keep margin for x-axis title */
    color: var(--light-text) !important; /* Ensure visibility in dark mode */
}

/* Light mode specific axis title colors */
.app-container.light-mode .js-plotly-plot .plotly .ytitle,
.app-container.light-mode div[class*="plotly"] .ytitle,
.app-container.light-mode .dash-graph .ytitle,
.app-container.light-mode .js-plotly-plot .plotly .xtitle,
.app-container.light-mode div[class*="plotly"] .xtitle,
.app-container.light-mode .dash-graph .xtitle {
    color: var(--dark-text) !important; /* Dark text for light mode */
}

/* Hide any unwanted annotations */
.js-plotly-plot .plotly .annotation,
div[class*="plotly"] .annotation {
    display: none !important;
}

/* Ensure data table has consistent number formatting */
.data-table td[data-dash-column^="avg_"],
.data-table td[data-dash-column^="total_"] {
    text-align: right;
    font-variant-numeric: tabular-nums; /* Apply here too */
}

/* Add platform information to visualization titles */
.chart-title[data-platform]::after {
    content: " on " attr(data-platform);
    font-weight: normal;
    opacity: 0.8; /* Make it slightly less prominent */
    font-size: 0.9em; /* Slightly smaller */
}

/* Enhanced Key Insights Section Styling */
.key-insights-fixed-section {
    position: sticky;
    bottom: 0;
    background-color: var(--dark-bg);
    border-top: 1px solid var(--border-color);
    padding: 0;
    margin-top: 1rem;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
    z-index: 10; /* Ensure it stays above other content */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Add shadow for visual separation */
}

.app-container.light-mode .key-insights-fixed-section {
    background-color: var(--light-bg);
    border-top: 1px solid var(--light-border-color);
}

/* Visual separator with gradient */
.key-insights-visual-separator {
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    margin-bottom: 0;
}

.app-container.light-mode .key-insights-visual-separator {
    background: linear-gradient(90deg, var(--light-primary-color), var(--light-secondary-color));
}

/* Title bar with flex layout */
.key-insights-title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.app-container.light-mode .key-insights-title-bar {
    border-bottom: 1px solid var(--light-border-color);
}

.key-insights-title {
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

/* Settings area for metric selection */
.key-insights-settings {
    display: flex;
    align-items: center;
}

.metric-selector {
    font-size: 0.8rem;
}

/* Content area with scrollable design */
.key-insights-content {
    padding: 1rem;
    max-height: 200px; /* Limit height */
    overflow-y: auto; /* Make scrollable */
}

/* Style for individual insight items */
.insight-item {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
    font-size: 0.9rem;
    line-height: 1.4;
}

.insight-item:before {
    content: '•';
    position: absolute;
    left: 0.5rem;
    color: var(--primary-color);
}

.app-container.light-mode .insight-item:before {
    color: var(--light-primary-color);
}
