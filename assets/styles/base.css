/* Base styles and variables for the Euranet Dashboard */

:root {
    --primary-color: #62C6D9;
    --secondary-color: #9D65FF;
    --accent-color: #53D8B2;
    --dark-bg: #0F1729;
    --card-bg: #1A2235;
    --light-text: #FFFFFF;
    --dark-text: #333333;
    --grid-color: #2A3347;
    --border-color: #2A3347;
    --hover-color: #2A3347;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --transition-speed: 0.3s;

    /* Light Mode Colors */
    --light-bg: #F5F7FF;
    --light-card-bg: #FFFFFF;
    --light-border-color: #E5E5EA;
    --light-hover-color: #EAEAEA;
    --light-primary-color: #5A9BD5; /* Adjusted light primary */
    --light-secondary-color: #7A57D1; /* Adjusted light secondary */
    --light-accent-color: #4CAF50; /* Adjusted light accent */
}

/* Global Styles */
body { /* Apply base styles directly to body */
    font-family: 'Inter', Arial, sans-serif;
    margin: 0;
    padding: 0;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* Apply theme variables based on class */
.app-container.dark-mode {
    background-color: var(--dark-bg);
    color: var(--light-text);
}

.app-container.light-mode {
    background-color: var(--light-bg);
    color: var(--dark-text);
}

/* Keyframes for message animation */
@keyframes fadeInSlideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add pulsing animation to the spinner */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Scrollbar Styling */
.visualization-container::-webkit-scrollbar,
.chat-container::-webkit-scrollbar { /* Apply to chat container too */
    width: 8px; /* Slightly wider */
}
.visualization-container::-webkit-scrollbar-thumb,
.chat-container::-webkit-scrollbar-thumb {
    border-radius: 4px;
    transition: background-color var(--transition-speed);
}
.visualization-container::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track {
    border-radius: 4px;
    transition: background-color var(--transition-speed);
}
/* Theme-specific scrollbars */
.app-container.dark-mode ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}
.app-container.dark-mode ::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.05);
}
.app-container.light-mode ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
}
.app-container.light-mode ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}
