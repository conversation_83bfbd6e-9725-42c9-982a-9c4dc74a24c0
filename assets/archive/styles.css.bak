:root {
    --primary-color: #62C6D9;
    --secondary-color: #9D65FF;
    --accent-color: #53D8B2;
    --dark-bg: #0F1729;
    --card-bg: #1A2235;
    --light-text: #FFFFFF;
    --dark-text: #333333;
    --grid-color: #2A3347;
    --border-color: #2A3347;
    --hover-color: #2A3347;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --transition-speed: 0.3s;

    /* Light Mode Colors */
    --light-bg: #F5F7FF;
    --light-card-bg: #FFFFFF;
    --light-border-color: #E5E5EA;
    --light-hover-color: #EAEAEA;
    --light-primary-color: #5A9BD5; /* Adjusted light primary */
    --light-secondary-color: #7A57D1; /* Adjusted light secondary */
    --light-accent-color: #4CAF50; /* Adjusted light accent */
}

/* Global Styles */
body { /* Apply base styles directly to body */
    font-family: 'Inter', Arial, sans-serif;
    margin: 0;
    padding: 0;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* Apply theme variables based on class */
.app-container.dark-mode {
    background-color: var(--dark-bg);
    color: var(--light-text);
}

.app-container.light-mode {
    background-color: var(--light-bg);
    color: var(--dark-text);
}

/* Keyframes for message animation */
@keyframes fadeInSlideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


/* Layout */
.app-container { /* Keep layout styles */
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main-content {
    display: flex;
    flex: 1;
    height: calc(100vh - 60px);
}

/* Header */
.header {
    display: flex;
    align-items: center; /* Vertically center items */
    padding: 0.5rem 1.5rem; /* Increase horizontal padding */
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color); /* Default border */
    height: 60px;
}
/* Theme-specific header */
.app-container.dark-mode .header {
    background-color: var(--card-bg);
    border-bottom-color: var(--border-color);
}
.app-container.light-mode .header {
    background-color: var(--light-card-bg);
    border-bottom-color: var(--light-border-color);
}


.logo { /* Keep logo styles */
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    margin-right: 0.5rem;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
}

.header-controls {
    margin-left: auto;
    display: flex;
    align-items: center;
}

/* Theme Toggle Button */
.theme-toggle-button {
    border-radius: 50% !important; /* Ensure it's round */
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
/* Dark mode button style */
.app-container.dark-mode .theme-toggle-button {
    color: var(--light-text);
    border-color: var(--secondary-color);
}
.app-container.dark-mode .theme-toggle-button:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}
/* Light mode button style */
.app-container.light-mode .theme-toggle-button {
    color: var(--dark-text);
    border-color: var(--light-secondary-color);
}
.app-container.light-mode .theme-toggle-button:hover {
    background-color: var(--light-secondary-color);
    color: var(--light-text); /* White icon on hover */
}


/* Chat Panel */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--border-color); /* Default border */
    height: 100%;
    max-width: 50%;
    /* Ensure panel itself uses flex */
    display: flex;
    flex-direction: column;
    height: 100%; /* Make panel fill height */
}
/* Theme-specific border */
.app-container.dark-mode .chat-panel {
    border-right-color: var(--border-color);
}
.app-container.light-mode .chat-panel {
    border-right-color: var(--light-border-color);
}

/* Header Icon Styling */
.header-icon-container {
    margin-right: 0.75rem; /* Space between icon and text */
    display: flex;
    align-items: center;
    justify-content: center;
}
.header-icon {
    font-size: 1.5rem; /* Adjust icon size */
    color: var(--primary-color); /* Use primary color */
    transition: color var(--transition-speed);
}
.app-container.light-mode .header-icon {
    color: var(--light-primary-color);
}


/* Chat Header (If needed later, currently using main header) */
/* .chat-header { ... } */
/* .chat-header-icon { ... } */
/* .chat-header-text { ... } */
/* .chat-header-subtext { ... } */

/* Header Title Styling */
.header-title { /* Renamed from .chat-header-text for clarity */
    font-size: 1.15rem; /* Slightly larger */
    font-weight: 600;
    margin: 0; /* Remove default margins */
    line-height: 1.2;
}

.header-subtitle { /* Renamed from .chat-header-subtext */
    font-size: 0.75rem;
    opacity: 0.7;
    margin: 0; /* Remove default margins */
    line-height: 1.2;
}

/* Remove old rules if they existed under different names */
/* .chat-header-text { ... } */
/* .chat-header-subtext { ... } */


.chat-container { /* Corrected selector */
    flex-grow: 1; /* Let it grow */
    flex-shrink: 1; /* Allow shrinking */
    flex-basis: auto; /* Default basis */
    overflow-y: auto; /* Enable scrolling */
    padding: 1.5rem; /* Increase padding */
    display: flex; /* Use flexbox for alignment */
    flex-direction: column; /* Stack messages vertically */
    min-height: 0; /* Prevent flex item from overflowing */
}

.message {
    margin-bottom: 1rem;
    padding: 0.8rem 1.1rem; /* Slightly adjust padding */
    border-radius: 0.75rem; /* Softer corners */
    max-width: 85%; /* Slightly wider */
    position: relative; /* For timestamp positioning */
    word-wrap: break-word; /* Ensure long words break */
    transition: background-color var(--transition-speed), color var(--transition-speed);
    animation: fadeInSlideUp 0.4s ease-out forwards; /* Apply animation */
}

/* Remove avatar for simplicity for now */
/* .message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-right: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: var(--light-text);
    font-weight: 600;
    font-size: 0.75rem;
} */

/* Theme-specific message styling */
.app-container.dark-mode .assistant-message {
    background-color: var(--card-bg);
    color: var(--light-text);
    align-self: flex-start; /* Align bot messages left */
}
.app-container.dark-mode .user-message {
    background-color: var(--primary-color);
    color: var(--light-text); /* Ensure text is light on primary bg */
    align-self: flex-end !important; /* Re-add !important */
}

.app-container.light-mode .assistant-message {
    background-color: var(--light-card-bg);
    color: var(--dark-text);
    border: 1px solid var(--light-border-color); /* Add subtle border */
    align-self: flex-start;
}
.app-container.light-mode .user-message {
    background-color: var(--light-primary-color);
    color: var(--light-text); /* Ensure text is light on primary bg */
    align-self: flex-end !important; /* Re-add !important */
}


.message-content { /* Style the inner content div */
     line-height: 1.5;
     padding-bottom: 1.5rem; /* Space for timestamp */
}

.message-timestamp { /* Position timestamp */
    font-size: 0.7rem;
    opacity: 0.7;
    position: absolute;
    bottom: 0.5rem;
    right: 0.75rem;
}

/* Chat Input Area */
.chat-input-area { /* Renamed from .chat-input */
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color); /* Default border */
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Space between input and button */
    transition: border-color var(--transition-speed);
}
/* Theme-specific border */
.app-container.dark-mode .chat-input-area {
    border-top-color: var(--border-color);
}
.app-container.light-mode .chat-input-area {
    border-top-color: var(--light-border-color);
}

/* Input Field Styling (using dbc class .chat-input) */
.chat-input {
    flex: 1;
    border-radius: 1.5rem !important; /* Use !important if needed */
    border: 1px solid var(--border-color) !important;
    padding: 0.6rem 1rem !important;
    resize: none;
    min-height: 40px !important; /* Fixed height */
    line-height: 1.4;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
/* Theme-specific input field */
.app-container.dark-mode .chat-input {
    background-color: var(--card-bg) !important;
    color: var(--light-text) !important;
    border-color: var(--border-color) !important;
}
.app-container.light-mode .chat-input {
    background-color: var(--light-card-bg) !important;
    color: var(--dark-text) !important;
    border-color: var(--light-border-color) !important;
}
.app-container.dark-mode .chat-input:focus {
    outline: none !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(98, 198, 217, 0.3); /* Subtle focus glow */
}
.app-container.light-mode .chat-input:focus {
    outline: none !important;
    border-color: var(--light-primary-color) !important;
    box-shadow: 0 0 0 2px rgba(90, 155, 213, 0.3); /* Subtle focus glow */
}


/* Send Button Styling (using dbc class .send-button) */
.send-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important; /* Remove default padding */
    transition: background-color var(--transition-speed), color var(--transition-speed);
}
/* Theme-specific send button */
.app-container.dark-mode .send-button {
    background-color: var(--primary-color) !important;
    color: var(--light-text) !important;
    border: none !important;
}
.app-container.dark-mode .send-button:hover {
    background-color: var(--secondary-color) !important;
}
.app-container.light-mode .send-button {
    background-color: var(--light-primary-color) !important;
    color: var(--light-text) !important;
    border: none !important;
}
.app-container.light-mode .send-button:hover {
    background-color: var(--light-secondary-color) !important;
}
.send-button i { /* Style the icon inside */
    font-size: 1rem;
}

/* Loading Indicator */
.loading-indicator-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem 1rem 0; /* Padding top only */
    height: 30px; /* Fixed height to prevent layout shifts */
}
.loading-spinner {
    opacity: 1; /* Increased opacity for better visibility */
    transform: scale(1.2); /* Make spinner slightly larger */
}

/* Add pulsing animation to the spinner */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-spinner > div {
    animation: pulse 1.5s infinite ease-in-out; /* Apply pulsing animation */
}

/* Ensure bottom sections in chat panel do not grow */
.suggestion-section,
.loading-indicator-container,
.chat-input-area {
    flex: 0 0 auto; /* Prevent growing or shrinking */
}


/* Visualization Panel */
.visualization-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    display: flex; /* Ensure panel uses flex */
    flex-direction: column; /* Stack items vertically */
    position: relative; /* For absolute positioning of fixed elements */
}

/* Visualization Header (Now part of .query-info) */
/* .visualization-header { ... removed ... } */

/* Visualization Title (Now part of .query-info) */
/* .visualization-title { ... removed ... } */

/* Visualization Subtitle (Now part of .query-info) */
/* .visualization-subtitle { ... removed ... } */

.visualization-container {
    flex-grow: 1; /* Let it grow */
    flex-shrink: 1; /* Allow shrinking */
    flex-basis: auto; /* Default basis */
    overflow-y: auto; /* Allows scrolling */
    padding: 1.5rem 2rem; /* Increase horizontal padding */
    min-height: 0; /* Prevent flex item from overflowing */
    padding-bottom: calc(1.5rem + 180px); /* Increased padding to prevent content from being hidden */
}

/* Ensure bottom sections in viz panel do not grow */
.key-insights-section, /* If it should be fixed bottom */
.tech-details-accordion-container {
    flex: 0 0 auto; /* Prevent growing or shrinking */
}


/* Empty State Styling */
.empty-state-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    padding: 2rem;
    border-radius: 8px;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}
.app-container.dark-mode .empty-state-container {
    background-color: var(--card-bg);
    border: 1px dashed var(--border-color);
}
.app-container.light-mode .empty-state-container {
    background-color: var(--light-card-bg);
    border: 1px dashed var(--light-border-color);
}
.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}
.empty-state-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: color var(--transition-speed);
}
.app-container.dark-mode .empty-state-title {
    color: var(--primary-color);
}
.app-container.light-mode .empty-state-title {
    color: var(--light-primary-color);
}
.empty-state-text {
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Card Styling for Visualization Panel Sections */
.chart-container,
.data-table-section,
.key-insights-section,
.query-info { /* Apply card style to query info too */
    border-radius: 8px; /* Consistent radius */
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: background-color var(--transition-speed), box-shadow var(--transition-speed);
    box-shadow: 0 1px 3px var(--shadow-color); /* Subtle shadow */
}
.app-container.dark-mode .chart-container,
.app-container.dark-mode .data-table-section,
.app-container.dark-mode .key-insights-section,
.app-container.dark-mode .query-info {
    background-color: var(--card-bg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow in dark mode */
}
.app-container.light-mode .chart-container,
.app-container.light-mode .data-table-section,
.app-container.light-mode .key-insights-section,
.app-container.light-mode .query-info {
    background-color: var(--light-card-bg);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Lighter shadow in light mode */
}


.chart-container {
    margin-bottom: 2rem; /* Add extra margin to ensure space between chart and other elements */
}
/* Remove theme-specific background colors as they are handled by card style */
/* .app-container.dark-mode .chart-container { ... } */
/* .app-container.light-mode .chart-container { ... } */

/* Chart Title - Consolidated and Adjusted */
.chart-title {
    font-size: 1.1rem; /* Slightly larger */
    font-weight: 600;
    margin-bottom: 1rem; /* Adjusted margin */
    transition: color var(--transition-speed);
}
.app-container.dark-mode .chart-title {
    color: var(--light-text);
}
.app-container.light-mode .chart-title {
    color: var(--dark-text);
}

.visualization-subtitle {
    font-size: 0.75rem;
    opacity: 0.7;
}

.visualization-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* .chart-container {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Remove duplicate .chart-title rule */
/* .chart-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    transition: color var(--transition-speed);
}
.app-container.dark-mode .chart-title {
    color: var(--light-text);
}
.app-container.light-mode .chart-title {
    color: var(--dark-text);
} */


.data-table-section {
    margin-bottom: 2rem; /* Add extra margin to ensure space between table and key insights */
}
/* Remove theme-specific background colors as they are handled by card style */
/* .app-container.dark-mode .data-table-section { ... } */
/* .app-container.light-mode .data-table-section { ... } */


.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem; /* Increased margin */
}

.data-table-title {
    font-size: 1.1rem; /* Slightly larger */
    font-weight: 600;
    margin: 0;
    transition: color var(--transition-speed);
}
.app-container.dark-mode .data-table-title {
    color: var(--light-text);
}
.app-container.light-mode .data-table-title {
    color: var(--dark-text);
}


.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: transparent; /* Ensure table itself doesn't override card bg */
}

/* Theme-specific table borders and header */
.app-container.dark-mode .data-table th,
.app-container.dark-mode .data-table td {
    border-color: var(--border-color);
}
.app-container.light-mode .data-table th,
.app-container.light-mode .data-table td {
    border-color: var(--light-border-color);
}
.app-container.dark-mode .data-table th {
    background-color: rgba(255, 255, 255, 0.05); /* Darker header */
    color: var(--light-text); /* Ensure header text is light */
}
.app-container.light-mode .data-table th {
    background-color: rgba(0, 0, 0, 0.03); /* Lighter header */
    color: var(--dark-text); /* Ensure header text is dark */
}
/* Ensure table body and cells inherit correct background/color */
.app-container.dark-mode .data-table tbody,
.app-container.dark-mode .data-table td {
    background-color: transparent !important; /* Ensure transparency */
    color: var(--light-text) !important;
}
.app-container.light-mode .data-table tbody,
.app-container.light-mode .data-table td {
    background-color: transparent !important; /* Ensure transparency */
    color: var(--dark-text) !important;
}


.data-table th {
    text-align: left;
    padding: 0.75rem 1rem; /* Adjusted padding */
    font-weight: 600;
    font-size: 0.9rem; /* Slightly smaller header font */
}

.data-table td {
    padding: 0.75rem 1rem; /* Adjusted padding */
    font-size: 0.85rem; /* Slightly smaller cell font */
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Add theme-specific hover effect for table rows */
.app-container.dark-mode .data-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.08) !important; /* Slightly lighter hover */
    color: var(--light-text);
}
.app-container.light-mode .data-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05) !important; /* Slightly darker hover */
    color: var(--dark-text);
}

/* Hide potential stray button near data table */
.data-table-section > button {
    display: none !important;
}


/* Key Insights - For insights within the scrollable area */
/* We're now using key-insights-fixed-section instead of key-insights-section */

/* Fixed Key Insights Section at the bottom */
.key-insights-fixed-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10; /* Ensure it's above other content */
    padding: 1rem 1.5rem; /* Match card padding */
    margin: 0; /* No margin needed */
    border-top: 4px solid var(--primary-color); /* Thicker border for better visibility */
    box-shadow: 0 -6px 12px rgba(0, 0, 0, 0.25); /* Stronger shadow for better separation */
    background-color: var(--card-bg); /* Match card background */
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
    /* Add a clear visual separator */
    position: relative; /* For the separator */
}
/* Add a clear visual separator line before the key insights section */
.key-insights-fixed-section::before {
    content: '';
    position: absolute;
    top: -1px; /* Position it at the top of the section */
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--border-color); /* Use border color for the line */
    z-index: 11; /* Above the section itself */
}

/* Theme-specific styling for key insights section */
.app-container.dark-mode .key-insights-fixed-section {
    background-color: var(--dark-bg); /* Use darker background for better contrast */
    border-top-color: var(--primary-color);
}
.app-container.light-mode .key-insights-fixed-section {
    background-color: var(--light-bg); /* Use lighter background for better contrast */
    border-top-color: var(--light-primary-color);
}
.app-container.dark-mode .key-insights-fixed-section::before {
    background-color: var(--border-color);
}
.app-container.light-mode .key-insights-fixed-section::before {
    background-color: var(--light-border-color);
}


.key-insights-title {
    font-size: 1.1rem; /* Slightly larger */
    font-weight: 600;
    margin-bottom: 0.75rem; /* Reduced margin for fixed section */
    transition: color var(--transition-speed);
    display: flex;
    align-items: center;
}

/* Style for the key insights content in the fixed section */
.key-insights-content {
    max-height: 120px; /* Allow some space for scrolling */
    overflow-y: auto; /* Enable scrolling if content is too long */
}

/* Style for the horizontal separator in key insights */
.key-insights-separator {
    border: none;
    height: 2px;
    background-color: var(--border-color);
    margin: 0 0 0.75rem 0;
    opacity: 0.7;
}

/* Theme-specific styling for the separator */
.app-container.dark-mode .key-insights-separator {
    background-color: var(--border-color);
}
.app-container.light-mode .key-insights-separator {
    background-color: var(--light-border-color);
}
.app-container.dark-mode .key-insights-title {
    color: var(--light-text);
}
.app-container.light-mode .key-insights-title {
    color: var(--dark-text);
}


.insight-item {
    margin-bottom: 0.5rem; /* Reduced margin for fixed section */
    display: flex;
    align-items: flex-start;
    font-size: 0.85rem; /* Slightly smaller for fixed section */
}

.insight-icon {
    margin-right: 0.75rem; /* Increased spacing */
    margin-top: 0.15rem; /* Align icon better */
    transition: color var(--transition-speed);
}
.app-container.dark-mode .insight-icon {
    color: var(--primary-color);
}
.app-container.light-mode .insight-icon {
    color: var(--light-primary-color);
}


.insight-text {
    line-height: 1.6; /* Improved readability */
}

/* Remove divider */
/* .visualization-divider { ... removed ... } */

/* Suggestions Area */
.suggestion-section { /* Renamed from .suggestions-container */
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color); /* Default border */
    transition: border-color var(--transition-speed);
}
.app-container.dark-mode .suggestion-section {
    border-top-color: var(--border-color);
}
.app-container.light-mode .suggestion-section {
    border-top-color: var(--light-border-color);
}


.suggestion-title { /* Renamed from .suggestions-title */
    font-size: 0.8rem;
    font-weight: 500; /* Slightly bolder */
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    opacity: 0.8; /* Slightly less prominent */
}

/* Remove icon for simplicity */
/* .suggestions-icon { ... removed ... } */

/* Suggestions List (using dbc class .suggestion-pill) */
.suggestion-section .d-flex { /* Target the flex container dbc creates */
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-pill { /* Renamed from .suggestion-button */
    border-radius: 1rem !important;
    padding: 0.35rem 0.9rem !important; /* Fine-tune padding */
    font-size: 0.78rem !important; /* Slightly larger font */
    font-weight: 500 !important; /* Medium weight */
    cursor: pointer;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed), transform 0.1s ease-out; /* Add transform transition */
    line-height: 1.4; /* Ensure consistent line height */
    border-width: 1px !important; /* Ensure border width is consistent */
    border-style: solid !important;
}
/* Theme-specific suggestion pills */
.app-container.dark-mode .suggestion-pill {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--light-text) !important;
}
.app-container.dark-mode .suggestion-pill:hover {
    background-color: var(--hover-color) !important;
    border-color: var(--hover-color) !important;
    transform: translateY(-1px); /* Slight lift on hover */
}
.app-container.light-mode .suggestion-pill {
    background-color: var(--light-card-bg) !important;
    border: 1px solid var(--light-border-color) !important;
    color: var(--dark-text) !important;
}
.app-container.light-mode .suggestion-pill:hover {
    background-color: var(--light-hover-color) !important;
    border-color: var(--light-hover-color) !important;
    transform: translateY(-1px); /* Slight lift on hover */
}

/* Technical Details Accordion */
.tech-details-accordion-container {
    margin-top: 1.5rem;
}
.tech-details-accordion .accordion-button {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}
.tech-details-accordion .accordion-body {
    font-size: 0.8rem;
    padding: 1rem;
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}
.tech-details-accordion .accordion-body code { /* Style code blocks */
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
}

/* Theme-specific accordion */
.app-container.dark-mode .tech-details-accordion .accordion-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-button {
    background-color: var(--card-bg);
    color: var(--light-text);
}
.app-container.dark-mode .tech-details-accordion .accordion-button:not(.collapsed) {
    background-color: var(--hover-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-body {
    background-color: var(--dark-bg); /* Slightly darker body */
    color: var(--light-text);
    border-top: 1px solid var(--border-color);
}
.app-container.dark-mode .tech-details-accordion .accordion-body code {
    background-color: rgba(255, 255, 255, 0.1);
    color: #cdd5f5; /* Light blueish code text */
}


.app-container.light-mode .tech-details-accordion .accordion-item {
    background-color: var(--light-card-bg);
    border-color: var(--light-border-color);
}
.app-container.light-mode .tech-details-accordion .accordion-button {
    background-color: var(--light-card-bg);
    color: var(--dark-text);
}
.app-container.light-mode .tech-details-accordion .accordion-button:not(.collapsed) {
    background-color: var(--light-hover-color);
}
.app-container.light-mode .tech-details-accordion .accordion-body {
    background-color: #f8f9fa; /* Slightly off-white body */
    color: var(--dark-text);
    border-top: 1px solid var(--light-border-color);
}
.app-container.light-mode .tech-details-accordion .accordion-body code {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333;
}


/* Plotly Chart Fixes (Keep these specific fixes) */
/* Apply !important cautiously, only where necessary to override Plotly defaults */
.js-plotly-plot .plotly .xtick text,
div[class*="plotly"] .xtick text,
.dash-graph .xtick text {
    transform: rotate(-45deg) !important;
    text-anchor: end !important;
    font-size: 10px !important;
    font-family: 'Inter', Arial, sans-serif !important;
}

/* Increase bottom margin for charts to accommodate rotated labels */
/* Apply margin to the container, not the plot itself */
.chart-container .js-plotly-plot,
.chart-container div[class*="plotly"],
.chart-container .dash-graph {
    margin-bottom: 60px !important; /* Reduced margin slightly */
    height: 500px !important; /* Fixed height to prevent stretching */
}

/* Ensure the SVG inside the chart maintains aspect ratio */
.chart-container .js-plotly-plot .plot-container .svg-container {
    height: 500px !important;
}

/* Fix chart container to prevent stretching */
/* These styles are now handled by theme-specific .chart-container rules */
/* .chart-container { ... removed duplicate/conflicting styles ... } */

/* Ensure the graph has fixed dimensions */
/* These styles are now handled by theme-specific .chart-container rules */
/* .chart-container .dash-graph { ... removed duplicate/conflicting styles ... } */


/* Fix axis labels */
.js-plotly-plot .plotly .ytitle,
div[class*="plotly"] .ytitle,
.dash-graph .ytitle {
    font-family: 'Inter', Arial, sans-serif !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.js-plotly-plot .plotly .xtitle,
div[class*="plotly"] .xtitle,
.dash-graph .xtitle {
    font-family: 'Inter', Arial, sans-serif !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 20px !important; /* Keep margin for x-axis title */
}

/* Fix data table styling */
/* These styles are now handled by theme-specific .data-table-section and .data-table rules */
/* .data-table-section { ... removed duplicate/conflicting styles ... } */
/* .data-table { ... removed duplicate/conflicting styles ... } */
/* .data-table th { ... removed duplicate/conflicting styles ... } */
/* .data-table td { ... removed duplicate/conflicting styles ... } */


/* Hide any unwanted annotations */
.js-plotly-plot .plotly .annotation,
div[class*="plotly"] .annotation {
    display: none !important;
}

/* Remove fixed navigation buttons for now */
/* .navigation-buttons { ... removed ... } */
/* .navigation-buttons:hover { ... removed ... } */


/* Scrollbar Styling */
.visualization-container::-webkit-scrollbar,
.chat-container::-webkit-scrollbar { /* Apply to chat container too */
    width: 8px; /* Slightly wider */
}
.visualization-container::-webkit-scrollbar-thumb,
.chat-container::-webkit-scrollbar-thumb {
    border-radius: 4px;
    transition: background-color var(--transition-speed);
}
.visualization-container::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track {
    border-radius: 4px;
    transition: background-color var(--transition-speed);
}
/* Theme-specific scrollbars */
.app-container.dark-mode ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}
.app-container.dark-mode ::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.05);
}
.app-container.light-mode ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
}
.app-container.light-mode ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}


/* Responsive Styles */
@media (max-width: 992px) { /* Adjusted breakpoint for better split */
    .chat-panel {
        max-width: 45%; /* Slightly adjust split */
    }
    .visualization-panel {
        max-width: 55%;
    }

    /* Adjust chart margins for medium screens */
    .chart-container .js-plotly-plot,
    .chart-container div[class*="plotly"],
    .chart-container .dash-graph {
        margin-bottom: 40px !important; /* Reduced margin */
        height: 450px !important; /* Slightly reduced height for medium screens */
    }

    /* Adjust SVG container height for medium screens */
    .chart-container .js-plotly-plot .plot-container .svg-container {
        height: 450px !important;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .chat-panel {
        max-width: 100%;
        height: 50vh;
        border-right: none; /* Remove border in column layout */
        border-bottom: 1px solid var(--border-color); /* Add bottom border */
    }
    /* Theme-specific border */
    .app-container.dark-mode .chat-panel {
        border-bottom-color: var(--border-color);
    }
    .app-container.light-mode .chat-panel {
        border-bottom-color: var(--light-border-color);
    }

    .visualization-panel {
        height: calc(100vh - 60px - 50vh); /* Adjust height calculation */
        max-width: 100%;
    }

    /* Improve mobile visualization container */
    .visualization-container {
        padding: 0.75rem; /* Reduce padding on mobile */
    }

    /* Adjust card padding for mobile */
    .chart-container,
    .data-table-section,
    .key-insights-section,
    .query-info {
        padding: 1rem; /* Reduce padding on mobile */
        margin-bottom: 1rem; /* Reduce margin on mobile */
    }

    /* Adjust chart margins for mobile */
    .chart-container .js-plotly-plot,
    .chart-container div[class*="plotly"],
    .chart-container .dash-graph {
        margin-bottom: 30px !important; /* Further reduced margin */
        height: 400px !important; /* Reduced height for mobile */
    }

    /* Adjust SVG container height for mobile */
    .chart-container .js-plotly-plot .plot-container .svg-container {
        height: 400px !important;
    }

    /* Adjust key insights fixed section for mobile */
    .key-insights-fixed-section {
        padding: 0.75rem 1rem; /* Reduced padding for mobile */
    }

    .key-insights-content {
        max-height: 100px; /* Reduced height for mobile */
    }

    /* Adjust visualization container padding for mobile */
    .visualization-container {
        padding-bottom: calc(1.5rem + 150px); /* Adjusted padding for mobile */
    }

    /* Improve table scrolling on mobile */
    .data-table-section {
        overflow-x: auto; /* Enable horizontal scrolling for tables */
    }

    /* Adjust header for mobile */
    .header {
        padding: 0.5rem 1rem; /* Reduce padding */
    }

    .header-title {
        font-size: 1rem; /* Smaller title on mobile */
    }

    .header-subtitle {
        font-size: 0.7rem; /* Smaller subtitle on mobile */
    }

    .header .d-none.d-lg-block { /* Ensure insights header part hides */
        display: none !important;
    }
    .header .col-auto:first-child { /* Allow chatbot title to take more space */
        flex-grow: 1;
    }

    /* Improve suggestion pills on mobile */
    .suggestion-pill {
        padding: 0.25rem 0.75rem !important; /* Smaller padding */
        font-size: 0.7rem !important; /* Smaller font */
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .chat-panel {
        height: 45vh; /* Give more space to visualization panel on very small screens */
    }

    .visualization-panel {
        height: calc(100vh - 60px - 45vh); /* Adjust height calculation */
    }

    /* Further reduce padding */
    .visualization-container {
        padding: 0.5rem;
    }

    /* Adjust chart height for very small screens */
    .chart-container .js-plotly-plot,
    .chart-container div[class*="plotly"],
    .chart-container .dash-graph {
        height: 350px !important; /* Further reduced height for very small screens */
        margin-bottom: 20px !important;
    }

    /* Adjust SVG container height for very small screens */
    .chart-container .js-plotly-plot .plot-container .svg-container {
        height: 350px !important;
    }

    /* Further adjust key insights fixed section for very small screens */
    .key-insights-fixed-section {
        padding: 0.5rem 0.75rem; /* Further reduced padding */
    }

    .key-insights-content {
        max-height: 80px; /* Further reduced height */
    }

    /* Adjust visualization container padding for very small screens */
    .visualization-container {
        padding-bottom: calc(0.75rem + 120px); /* Further adjusted padding for very small screens */
    }

    /* Make insight items more compact */
    .insight-item {
        margin-bottom: 0.25rem; /* Further reduced margin */
        font-size: 0.8rem; /* Smaller font size */
    }

    .key-insights-title {
        font-size: 1rem; /* Smaller title */
        margin-bottom: 0.5rem; /* Reduced margin */
    }

    /* Stack header elements on very small screens */
    .header > .col-6 {
        width: 100% !important;
        text-align: center;
        margin-bottom: 0.25rem;
    }

    .header {
        flex-direction: column;
        height: auto; /* Allow header to expand */
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }

    /* Center header content */
    .header > .col-6 > div {
        justify-content: center !important;
    }

    /* Adjust theme toggle button position */
    .theme-toggle-button {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        width: 32px !important;
        height: 32px !important;
    }
}


/* Remove redundant light mode overrides */
/* body.light-mode { ... removed ... } */
/* body.light-mode .header, ... etc ... { ... removed ... } */
/* body.light-mode .border-color, ... etc ... { ... removed ... } */
/* body.light-mode .theme-toggle { ... removed ... } */


/* Ensure data table has consistent number formatting */
.data-table td[data-dash-column^="avg_"],
.data-table td[data-dash-column^="total_"] {
    text-align: right;
    font-variant-numeric: tabular-nums; /* Apply here too */
}

/* Format numbers in data table */
/* This is handled by the python formatting now */
/* .data-table-section .dash-table-container td { ... removed ... } */

/* Add platform information to visualization titles */
.chart-title[data-platform]::after {
    content: " on " attr(data-platform);
    font-weight: normal;
    opacity: 0.8; /* Make it slightly less prominent */
    font-size: 0.9em; /* Slightly smaller */
}

/* Improve spacing in visualization header (now query-info) */
/* .visualization-header { ... removed ... } */

/* .current-query { ... removed ... } */

.query-info {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem; /* Adjusted padding */
    border-radius: 0.5rem;
    border-left-width: 4px;
    border-left-style: solid;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}
.app-container.dark-mode .query-info {
    background-color: var(--card-bg);
    border-left-color: var(--primary-color);
}
.app-container.light-mode .query-info {
    background-color: var(--light-card-bg);
    border-left-color: var(--light-primary-color);
}


.subtitle { /* Used in query-info and empty-state */
    font-size: 0.8rem;
    text-transform: uppercase;
    opacity: 0.7;
    margin-bottom: 0.25rem;
    font-weight: 500; /* Slightly bolder */
}

.query-text {
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 0;
    font-style: italic; /* Italicize the query */
}

/* Ensure consistent spacing in insights */
.insight-item {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* Ensure consistent number formatting in insights */
/* Handled in Python formatting */
/* .insight-text [data-value] { ... removed ... } */
