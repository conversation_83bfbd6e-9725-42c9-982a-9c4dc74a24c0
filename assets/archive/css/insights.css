/* Insights CSS for Euranet Dashboard */

/* Key Insights Section */
.key-insights-fixed-section {
    position: relative;
    border-top: 1px solid var(--grid-color);
    background-color: var(--card-bg-color);
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 200px;
}

.key-insights-visual-separator {
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    width: 100%;
}

.key-insights-title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid var(--grid-color);
}

.key-insights-title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.key-insights-settings {
    display: flex;
    align-items: center;
}

.key-insights-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px 15px;
}

/* Adaptive Insights Layouts */

/* Simple Layout */
.insights-list {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.insight-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    border-left: 3px solid var(--primary-color);
    background-color: rgba(98, 198, 217, 0.1);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.insight-complexity-low {
    padding: 6px 10px;
    font-size: 14px;
}

.insight-complexity-medium {
    padding: 8px 12px;
    font-size: 14px;
}

.insight-complexity-high {
    padding: 10px 14px;
    font-size: 14px;
    font-weight: 500;
}

/* Tabbed Layout */
.insights-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.insights-tab {
    padding: 8px 12px;
}

.insights-tab-content {
    padding: 10px;
    overflow-y: auto;
}

.insights-category-comparison .nav-link.active {
    background-color: rgba(157, 101, 255, 0.2);
    color: var(--text-color);
    border-bottom: 2px solid var(--secondary-color);
}

.insights-category-trend .nav-link.active {
    background-color: rgba(83, 216, 178, 0.2);
    color: var(--text-color);
    border-bottom: 2px solid var(--accent-color);
}

.insights-category-statistical .nav-link.active {
    background-color: rgba(98, 172, 217, 0.2);
    color: var(--text-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Card Layout */
.insights-card-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.insight-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.insight-card-important {
    background-color: rgba(157, 101, 255, 0.2);
    border-left: 4px solid var(--secondary-color);
}

.insight-card-medium {
    background-color: rgba(83, 216, 178, 0.2);
    border-left: 3px solid var(--accent-color);
}

.insight-card-normal {
    background-color: rgba(98, 172, 217, 0.2);
    border-left: 2px solid var(--primary-color);
}

.insight-category-comparison {
    border-color: var(--secondary-color);
}

.insight-category-trend {
    border-color: var(--accent-color);
}

.insight-category-statistical {
    border-color: var(--primary-color);
}

/* No Insights Message */
.no-insights-message {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}
