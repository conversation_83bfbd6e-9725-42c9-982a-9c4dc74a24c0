/* Main CSS for Euranet Dashboard */

/* Import component-specific CSS */
@import url('components.css');
@import url('chat.css');
@import url('insights.css');
@import url('visualizations.css');

/* Global styles */
:root {
    --primary-color: #62C6D9;
    --secondary-color: #9D65FF;
    --accent-color: #53D8B2;
    --background-color: #0F1729;
    --card-bg-color: #1A2235;
    --text-color: #FFFFFF;
    --grid-color: #2A3347;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Inter', sans-serif;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.card {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
}

.subtitle {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 15px;
}

/* Button styles */
.button {
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.button:hover {
    background-color: var(--secondary-color);
}

/* Input styles */
input, select, textarea {
    background-color: var(--card-bg-color);
    border: 1px solid var(--grid-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    padding: 10px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

/* Dropdown styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: var(--card-bg-color);
    min-width: 160px;
    box-shadow: var(--box-shadow);
    z-index: 1;
    border-radius: var(--border-radius);
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* Grid layout */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Responsive layout */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
}
