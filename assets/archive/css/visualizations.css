/* Visualizations CSS for Euranet Dashboard */

/* Visualization Panel */
.visualization-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Visualization Container */
.visualization-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Empty State */
.empty-state-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px;
    text-align: center;
    color: var(--text-secondary);
}

/* Chart Container */
.chart-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
}

.chart-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Data Table */
.data-table-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 10px 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--grid-color);
}

.data-table td {
    padding: 8px 15px;
    border-bottom: 1px solid var(--grid-color);
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Plotly Customizations */
.js-plotly-plot .plotly .main-svg {
    background-color: transparent !important;
}

.js-plotly-plot .plotly .bg {
    fill: transparent !important;
}

.js-plotly-plot .plotly .xtick text,
.js-plotly-plot .plotly .ytick text {
    fill: var(--text-color) !important;
}

.js-plotly-plot .plotly .xgrid,
.js-plotly-plot .plotly .ygrid {
    stroke: var(--grid-color) !important;
    stroke-width: 1 !important;
}

.js-plotly-plot .plotly .xtick,
.js-plotly-plot .plotly .ytick {
    stroke: var(--grid-color) !important;
}

.js-plotly-plot .plotly .xaxis .title,
.js-plotly-plot .plotly .yaxis .title {
    fill: var(--text-color) !important;
    font-weight: 500 !important;
}

.js-plotly-plot .plotly .hoverlabel {
    font-size: 12px !important;
}

/* Enhanced Hover Effects */
.js-plotly-plot .plotly .hoverlabel {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--grid-color) !important;
    border-radius: var(--border-radius) !important;
    padding: 8px !important;
    box-shadow: var(--box-shadow) !important;
}

.js-plotly-plot .plotly .hoverlabel .hovertext {
    font-family: 'Inter', sans-serif !important;
}

/* Visualization Types */

/* Bar Chart */
.bar-chart .plotly .bars path {
    opacity: 0.9 !important;
}

/* Line Chart */
.line-chart .plotly .scatter .lines path {
    stroke-width: 3 !important;
}

.line-chart .plotly .scatter .points path {
    stroke-width: 1 !important;
}

/* Pie Chart */
.pie-chart .plotly .pie path {
    stroke-width: 1 !important;
    stroke: var(--background-color) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .chart-container, .data-table-container {
        padding: 15px;
    }
    
    .chart-title {
        font-size: 16px;
    }
    
    .data-table th, .data-table td {
        padding: 6px 10px;
    }
}
