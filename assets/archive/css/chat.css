/* Chat CSS for Euranet Dashboard */

/* Chat Panel */
.chat-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-right: 1px solid var(--grid-color);
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

/* Message Bubbles */
.message-bubble {
    max-width: 80%;
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    position: relative;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-message {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: var(--background-color);
    border-bottom-right-radius: 0;
}

.assistant-message {
    align-self: flex-start;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border-bottom-left-radius: 0;
}

.message-content {
    word-break: break-word;
}

.message-timestamp {
    font-size: 10px;
    opacity: 0.7;
    margin-top: 5px;
    text-align: right;
}

/* Suggestion Section */
.suggestion-section {
    padding: 10px 15px;
    border-top: 1px solid var(--grid-color);
    background-color: var(--card-bg-color);
}

.suggestion-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-button {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--grid-color);
    border-radius: var(--border-radius);
    padding: 6px 12px;
    font-size: 12px;
    transition: var(--transition);
    cursor: pointer;
}

.suggestion-button:hover {
    background-color: var(--primary-color);
    color: var(--background-color);
    border-color: var(--primary-color);
}

/* Loading Indicator */
.loading-indicator-container {
    padding: 10px;
    text-align: center;
}

.loading-dots {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.loading-dot {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: loadingDot 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loadingDot {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Chat Input Area */
.chat-input-area {
    display: flex;
    align-items: center;
    padding: 15px;
    border-top: 1px solid var(--grid-color);
    background-color: var(--card-bg-color);
}

.chat-input {
    flex: 1;
    background-color: var(--background-color);
    border: 1px solid var(--grid-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    padding: 10px 15px;
    font-size: 14px;
    transition: var(--transition);
}

.chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(98, 198, 217, 0.2);
}

.send-button {
    margin-left: 10px;
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.send-button:hover {
    background-color: var(--secondary-color);
    transform: scale(1.05);
}

.send-button:active {
    transform: scale(0.95);
}

/* Markdown Styling */
.markdown-content h1, .markdown-content h2, .markdown-content h3 {
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.markdown-content p {
    margin-bottom: 0.75em;
}

.markdown-content ul, .markdown-content ol {
    margin-bottom: 0.75em;
    padding-left: 1.5em;
}

.markdown-content code {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.markdown-content pre {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 10px;
    border-radius: var(--border-radius);
    overflow-x: auto;
    margin-bottom: 0.75em;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
}

.markdown-content blockquote {
    border-left: 3px solid var(--primary-color);
    padding-left: 10px;
    margin-left: 0;
    color: var(--text-secondary);
}

.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 0.75em;
}

.markdown-content th, .markdown-content td {
    border: 1px solid var(--grid-color);
    padding: 6px 10px;
}

.markdown-content th {
    background-color: rgba(0, 0, 0, 0.1);
}
