/* Component-specific CSS for Euranet Dashboard */

/* Metric Selector */
.metric-selector {
    margin-bottom: 15px;
}

.metric-selector-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.metric-selector-label {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.metric-selector-dropdown {
    min-width: 120px;
}

/* Make dropdown text and background match the theme */
.metric-selector .Select-control {
    background-color: var(--card-bg-color);
    border-color: var(--grid-color);
}

.metric-selector .Select-value-label {
    color: var(--text-color) !important;
}

.metric-selector .Select-menu-outer {
    background-color: var(--card-bg-color);
    border-color: var(--grid-color);
}

.metric-selector .Select-option {
    background-color: var(--card-bg-color);
    color: var(--text-color);
}

.metric-selector .Select-option.is-focused {
    background-color: var(--primary-color);
    color: var(--background-color);
}

/* Tooltip styling */
.tooltip {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--grid-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    font-size: 12px;
    max-width: 200px;
}
