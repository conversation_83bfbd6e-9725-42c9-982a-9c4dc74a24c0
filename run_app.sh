#!/bin/bash

# This script runs the Euranet Chatbot application

# Kill any existing Python processes running the app
pkill -f "python app.py" || true

# Create a virtual environment if it doesn't exist
if [ ! -d "venv_dash" ]; then
    echo "Creating virtual environment..."
    python -m venv venv_dash
    source venv_dash/bin/activate
    pip install dash dash-bootstrap-components pandas plotly flask
else
    source venv_dash/bin/activate
fi

# Run the application
echo "Starting the application on port 8051..."
python app.py
