# Euranet Dashboard

A Dash-based interactive dashboard for querying and visualizing Euranet podcast and social media data. This application provides a chat interface for natural language queries and displays results through interactive visualizations and data tables.

## Features

- **Interactive Chat Interface**: Ask questions about podcast and social media data using natural language
- **Gemma 3 1B Integration**: Enhanced query understanding and insight generation using Google's Gemma 3 1B model
- **ADK Integration**: Flexible metric calculations and enhanced UI components using Google's Agent Development Kit
- **Data Visualizations**: View query results through interactive Plotly charts
- **Data Tables**: Examine detailed data in tabular format
- **Key Insights**: Automatically generated insights based on query results with flexible metric selection
- **Dark/Light Mode**: Toggle between dark and light themes
- **Component-Based Architecture**: Follows MVC/MVP patterns for maintainability
- **Responsive Design**: Works on various screen sizes

## Project Structure

The application follows a Model-View-Controller (MVC) pattern with elements of Model-View-Presenter (MVP):

```
app.py                 # Main application entry point
components/            # Model layer - data processing and business logic
  ├── database.py      # Database connection and queries
  ├── gemma_model.py   # Gemma 3 1B model integration
  ├── adk_integration.py # ADK integration for enhanced functionality
  ├── query_processor.py # Query processing logic
  ├── suggestions.py   # Suggestion generation
  └── visualizer.py    # Visualization creation
layouts/               # View layer - UI components
  ├── main_layout.py   # Main application layout
  ├── chat_panel.py    # Chat interface layout
  └── visualization_panel.py # Data visualization layout
callbacks/             # Controller layer - event handling
  ├── chat_callbacks.py # Chat-related callbacks
  ├── visualization_callbacks.py # Visualization-related callbacks
  └── theme_callbacks.py # Theme-related callbacks
assets/                # Static assets
  ├── main.css         # Main CSS file that imports modular CSS
  └── styles/          # Modular CSS files
      ├── base.css     # Base styles and variables
      ├── layout.css   # Layout styles
      ├── components.css # Component styles
      └── visualizations.css # Visualization-specific styles
doc/                   # Documentation
  ├── dash_research.md # Research on Dash implementation
  ├── implementation_progress.md # Progress tracking
  ├── project_plan.md  # Project planning document
  ├── troubleshooting.md # Troubleshooting guide
  ├── gemma_integration.md # Gemma 3 1B integration documentation
  └── adk_integration.md # ADK integration documentation
```

## Setup and Installation

### Prerequisites

- Python 3.11 (Python 3.12 may have compatibility issues with some dependencies)
- SQLite database (euranet.db)

### Installation

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv_dash
   source venv_dash/bin/activate  # On Windows: venv_dash\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install dash dash-bootstrap-components pandas plotly flask
   ```

### Running the Application

```bash
# Using the run script
./run_app.sh

# Or manually
source venv_dash/bin/activate
python app.py
```

The application will be available at http://localhost:8052 by default.

## Key UI Features

### Chat Panel
- Message bubbles with different styling for user and assistant
- Support for Enter key to send messages
- Loading indicators
- Suggestion buttons
- Scrollable message container

### Visualization Panel
- Interactive Plotly charts with hover effects
- Data tables for detailed examination
- Fixed Key Insights section at the bottom
- Visual separation between components

## Documentation

For more detailed information, refer to the documentation in the `doc/` directory:

- `dash_research.md`: Research on Dash implementation
- `gemma_integration.md`: Details on Gemma 3 1B model integration
- `implementation_progress.md`: Current status and next steps
- `project_plan.md`: Project planning and progress tracking
- `troubleshooting.md`: Common issues and solutions

## Troubleshooting

If you encounter issues, please refer to the troubleshooting guide in `doc/troubleshooting.md`.
