"""
Metric Selector for Euranet Dashboard.
Provides a UI component for selecting metric types.
"""
import dash
from dash import html, dcc, callback, Input, Output, State
import dash_bootstrap_components as dbc
from components.metric_calculator import MetricCalculator

class MetricSelector:
    """UI component for selecting metric types."""

    def __init__(self, app, id_prefix="metric-selector"):
        """
        Initialize the metric selector.
        
        Args:
            app: The Dash app
            id_prefix: Prefix for component IDs
        """
        self.app = app
        self.id_prefix = id_prefix
        self.metric_calculator = MetricCalculator()
        
        # Register callbacks
        self._register_callbacks()
    
    def layout(self):
        """
        Get the layout for the metric selector.
        
        Returns:
            dash component: The layout
        """
        metric_types = self.metric_calculator.get_metric_types()
        default_metric_type = self.metric_calculator.get_default_metric_type()
        
        return html.Div([
            html.Div([
                html.Label("Metric Type:", className="metric-selector-label"),
                dcc.Dropdown(
                    id=f"{self.id_prefix}-dropdown",
                    options=[
                        {"label": self.metric_calculator.get_display_name(mt), "value": mt}
                        for mt in metric_types
                    ],
                    value=default_metric_type,
                    clearable=False,
                    className="metric-selector-dropdown"
                ),
            ], className="metric-selector-container"),
            
            # Hidden div to store the current metric type
            html.Div(id=f"{self.id_prefix}-current-type", style={"display": "none"}, children=default_metric_type),
            
            # Tooltip for metric type description
            dbc.Tooltip(
                id=f"{self.id_prefix}-tooltip",
                target=f"{self.id_prefix}-dropdown",
                placement="top"
            )
        ], className="metric-selector")
    
    def _register_callbacks(self):
        """Register callbacks for the metric selector."""
        
        # Update tooltip with metric type description
        @self.app.callback(
            Output(f"{self.id_prefix}-tooltip", "children"),
            Input(f"{self.id_prefix}-dropdown", "value")
        )
        def update_tooltip(metric_type):
            if not metric_type:
                return ""
            return self.metric_calculator.get_description(metric_type)
        
        # Update current metric type
        @self.app.callback(
            Output(f"{self.id_prefix}-current-type", "children"),
            Input(f"{self.id_prefix}-dropdown", "value")
        )
        def update_current_type(metric_type):
            return metric_type
    
    def get_current_metric_type(self):
        """
        Get the current metric type.
        
        Returns:
            str: The current metric type
        """
        return self.metric_calculator.get_default_metric_type()
    
    def add_metric_type_callback(self, outputs, inputs=None, state=None):
        """
        Add a callback that includes the current metric type.
        
        Args:
            outputs: The callback outputs
            inputs: Additional inputs for the callback
            state: Additional state for the callback
            
        Returns:
            function: Decorator for the callback function
        """
        if inputs is None:
            inputs = []
        
        if state is None:
            state = []
        
        # Add current metric type as input
        inputs.append(Input(f"{self.id_prefix}-current-type", "children"))
        
        # Register the callback
        return self.app.callback(outputs, inputs, state)
