"""
Metric Calculator for Euranet Dashboard.
Provides flexible calculation of metrics based on user selection.
"""
import pandas as pd
import numpy as np

class MetricCalculator:
    """Calculates metrics based on user selection."""

    def __init__(self):
        """Initialize the metric calculator."""
        # Available metric types
        self.metric_types = ["AVG", "SUM", "COUNT", "PERCENTAGE"]

        # Default metric type
        self.default_metric_type = "AVG"

        # Metric type display names
        self.metric_type_display_names = {
            "AVG": "Average",
            "SUM": "Total",
            "COUNT": "Count",
            "PERCENTAGE": "Percentage"
        }

        # Metric type descriptions
        self.metric_type_descriptions = {
            "AVG": "Average value across all entries",
            "SUM": "Sum of all values",
            "COUNT": "Count of entries",
            "PERCENTAGE": "Percentage of total"
        }

        # Metric type prefixes for column names
        self.metric_type_prefixes = {
            "AVG": "avg_",
            "SUM": "sum_",
            "COUNT": "count_",
            "PERCENTAGE": "pct_"
        }

        # SQL aggregation functions
        self.sql_aggregations = {
            "AVG": "AVG",
            "SUM": "SUM",
            "COUNT": "COUNT",
            "PERCENTAGE": "SUM"  # Percentage is calculated post-query
        }

    def get_metric_types(self):
        """
        Get all available metric types.

        Returns:
            list: List of metric types
        """
        return self.metric_types

    def get_default_metric_type(self):
        """
        Get the default metric type.

        Returns:
            str: Default metric type
        """
        return self.default_metric_type

    def get_display_name(self, metric_type):
        """
        Get the display name for a metric type.

        Args:
            metric_type: The metric type

        Returns:
            str: The display name for the metric type
        """
        return self.metric_type_display_names.get(metric_type, metric_type)

    def get_description(self, metric_type):
        """
        Get the description for a metric type.

        Args:
            metric_type: The metric type

        Returns:
            str: The description for the metric type
        """
        return self.metric_type_descriptions.get(metric_type, "")

    def get_column_prefix(self, metric_type):
        """
        Get the column prefix for a metric type.

        Args:
            metric_type: The metric type

        Returns:
            str: The column prefix for the metric type
        """
        return self.metric_type_prefixes.get(metric_type, "")

    def get_sql_aggregation(self, metric_type):
        """
        Get the SQL aggregation function for a metric type.

        Args:
            metric_type: The metric type

        Returns:
            str: The SQL aggregation function for the metric type
        """
        return self.sql_aggregations.get(metric_type, "AVG")

    def calculate_metric(self, data, metric_name, metric_type):
        """
        Calculate a metric based on the metric type.

        Args:
            data: DataFrame containing the data
            metric_name: The name of the metric column
            metric_type: The type of metric to calculate

        Returns:
            DataFrame: The data with the calculated metric
        """
        if data.empty:
            return data

        # Make a copy to avoid modifying the original
        result = data.copy()

        # Get the column prefix
        prefix = self.get_column_prefix(metric_type)

        # Check if the metric column exists
        if metric_name not in result.columns:
            # Try with different prefixes
            for p in self.metric_type_prefixes.values():
                if p + metric_name.replace(prefix, "") in result.columns:
                    metric_name = p + metric_name.replace(prefix, "")
                    break
            else:
                # If still not found, return the original data
                return result

        # Get the base metric name (without prefix)
        base_metric = metric_name
        for p in self.metric_type_prefixes.values():
            if metric_name.startswith(p):
                base_metric = metric_name[len(p):]
                break

        # Calculate the metric based on the type
        if metric_type == "AVG":
            # If the column already has avg_ prefix, use it directly
            if metric_name.startswith("avg_"):
                new_column = metric_name
            else:
                # Otherwise, calculate the average
                new_column = f"avg_{base_metric}"
                if new_column not in result.columns:
                    result[new_column] = result[metric_name].mean()

        elif metric_type == "SUM":
            # If the column already has sum_ prefix, use it directly
            if metric_name.startswith("sum_"):
                new_column = metric_name
            else:
                # Otherwise, calculate the sum
                new_column = f"sum_{base_metric}"
                if new_column not in result.columns:
                    result[new_column] = result[metric_name].sum()

        elif metric_type == "COUNT":
            # Calculate the count
            new_column = f"count_{base_metric}"
            if new_column not in result.columns:
                result[new_column] = len(result)

        elif metric_type == "PERCENTAGE":
            # Calculate the percentage
            new_column = f"pct_{base_metric}"
            if new_column not in result.columns:
                total = result[metric_name].sum()
                if total > 0:
                    result[new_column] = (result[metric_name] / total) * 100
                else:
                    result[new_column] = 0

        return result

    def update_sql_query(self, sql, metric_name, metric_type):
        """
        Update a SQL query to use the appropriate aggregation function.

        Args:
            sql: The SQL query
            metric_name: The name of the metric column
            metric_type: The type of metric to calculate

        Returns:
            str: The updated SQL query
        """
        # Get the SQL aggregation function
        agg_func = self.get_sql_aggregation(metric_type)

        # Get the column prefix
        prefix = self.get_column_prefix(metric_type)

        # Get the base metric name (without prefix)
        base_metric = metric_name
        for p in self.metric_type_prefixes.values():
            if metric_name.startswith(p):
                base_metric = metric_name[len(p):]
                break

        # Replace the aggregation function in the SQL query
        for old_agg in self.sql_aggregations.values():
            if f"{old_agg}({metric_name})" in sql:
                sql = sql.replace(f"{old_agg}({metric_name})", f"{agg_func}({metric_name})")
            elif f"{old_agg}({base_metric})" in sql:
                sql = sql.replace(f"{old_agg}({base_metric})", f"{agg_func}({base_metric})")

        # Update the column alias
        for old_prefix in self.metric_type_prefixes.values():
            if f"as {old_prefix}{base_metric}" in sql.lower():
                sql = sql.replace(f"as {old_prefix}{base_metric}", f"as {prefix}{base_metric}")
            elif f"AS {old_prefix}{base_metric}" in sql:
                sql = sql.replace(f"AS {old_prefix}{base_metric}", f"AS {prefix}{base_metric}")

        return sql

    def detect_metric_type_from_query(self, query):
        """
        Detect the metric type from a query.

        Args:
            query: The query text

        Returns:
            str: The detected metric type
        """
        query_lower = query.lower()

        if "average" in query_lower or "avg" in query_lower or "mean" in query_lower:
            return "AVG"
        elif "total" in query_lower or "sum" in query_lower:
            return "SUM"
        elif "count" in query_lower or "how many" in query_lower:
            return "COUNT"
        elif "percentage" in query_lower or "percent" in query_lower or "%" in query_lower:
            return "PERCENTAGE"

        # Default to AVG
        return self.default_metric_type
