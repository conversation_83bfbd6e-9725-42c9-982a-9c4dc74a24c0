"""
Database connector for Euranet Chatbot.
Handles connections to the SQLite database and query execution.
"""
import sqlite3
import pandas as pd

class DatabaseConnector:
    """Connector to the SQLite database for Euranet data."""
    
    def __init__(self, db_path):
        self.db_path = db_path
        
    def connect(self):
        """Connect to the database."""
        return sqlite3.connect(self.db_path)
    
    def execute_query(self, query):
        """Execute a SQL query and return the results as a DataFrame."""
        try:
            with self.connect() as conn:
                return pd.read_sql_query(query, conn)
        except Exception as e:
            print(f"Error executing query: {e}")
            return pd.DataFrame()
    
    def get_table_info(self):
        """Get information about tables in the database."""
        try:
            with self.connect() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                table_info = {}
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    table_info[table_name] = [col[1] for col in columns]
                    
            return table_info
        except Exception as e:
            print(f"Error getting table info: {e}")
            return {}
    
    def get_row_counts(self):
        """Get the number of rows in each table."""
        try:
            table_info = self.get_table_info()
            counts = {}
            
            with self.connect() as conn:
                cursor = conn.cursor()
                for table in table_info.keys():
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    counts[table] = cursor.fetchone()[0]
                    
            return counts
        except Exception as e:
            print(f"Error getting row counts: {e}")
            return {}
    
    def get_sample_data(self, table_name, limit=5):
        """Get sample data from a table."""
        query = f"SELECT * FROM {table_name} LIMIT {limit}"
        return self.execute_query(query)
