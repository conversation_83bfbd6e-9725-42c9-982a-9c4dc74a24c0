"""
Visualizer for Euranet Chatbot.
Creates and styles visualizations based on query results.
"""
import plotly.express as px
import plotly.graph_objects as go

class Visualizer:
    """Creates and styles visualizations with DB-inspired aesthetics."""

    def __init__(self, dark_mode=True):
        self.set_theme(dark_mode)

    def set_theme(self, dark_mode):
        """Set the theme based on dark/light mode."""
        self.dark_mode = dark_mode

        if dark_mode:
            self.colors = {
                "bg": "#1A2235",  # Lighter background for better contrast
                "card_bg": "#1A2235",
                "text": "#FFFFFF",
                "grid": "#2A3347",
                "palette": ["#62C6D9", "#9D65FF", "#53D8B2", "#FF9F65"]  # Enhanced contrast palette
            }
        else:
            self.colors = {
                "bg": "#F5F7FF",
                "card_bg": "#FFFFFF",
                "text": "#333333",
                "grid": "#E5E5EA",
                "palette": ["#5A9BD5", "#7A57D1", "#4CAF50", "#FF7043"]  # Enhanced contrast palette
            }

    def create_visualization(self, viz_config, data):
        """Create a visualization based on configuration."""
        if data.empty:
            return self._create_empty_chart(viz_config.get("title", "No Data Available"))

        viz_type = viz_config.get("type", "bar")
        x = viz_config.get("x", data.columns[0])
        y = viz_config.get("y", data.columns[1] if len(data.columns) > 1 else data.columns[0])
        title = viz_config.get("title", "Visualization")

        # Create the chart based on type
        if viz_type == "bar":
            return self._create_bar_chart(data, x, y, title)
        elif viz_type == "line":
            return self._create_line_chart(data, x, y, title)
        elif viz_type == "pie":
            return self._create_pie_chart(data, x, y, title)
        elif viz_type == "scatter":
            return self._create_scatter_chart(data, x, y, title)
        else:
            return self._create_bar_chart(data, x, y, title)

    def _get_layout(self, title):
        """Get common layout settings."""
        return {
            "title": {
                "text": title,
                "font": {"size": 16, "color": self.colors["text"], "family": "Inter, Arial, sans-serif"},
                "y": 0.95
            },
            "margin": {"t": 60, "l": 50, "r": 30, "b": 80},  # Increased bottom margin for labels
            "hovermode": "closest",
            "paper_bgcolor": "rgba(0,0,0,0)",
            "plot_bgcolor": self.colors["bg"],
            "font": {"family": "Inter, Arial, sans-serif", "size": 12, "color": self.colors["text"]},
            "xaxis": {
                "gridcolor": self.colors["grid"],
                "tickangle": -45,
                "automargin": True,
                "zerolinecolor": self.colors["grid"],
                "fixedrange": True,
                "title": {"standoff": 25},  # Increased standoff for better spacing
                "tickfont": {"size": 10},
                "showgrid": True,  # Show grid for better readability
                "layer": "below traces"  # Ensure grid is behind the data
            },
            "yaxis": {
                "gridcolor": self.colors["grid"],
                "zerolinecolor": self.colors["grid"],
                "fixedrange": True,
                "title": {"standoff": 15},
                "showgrid": True,
                "layer": "below traces"
            },
            "colorway": self.colors["palette"],
            "dragmode": False
        }

    def _create_bar_chart(self, data, x, y, title):
        """Create a bar chart with DB-inspired styling."""
        # Format y values for better readability if they're numeric
        if y in data.columns and data[y].dtype.kind in 'ifc':
            # Check if values are large
            max_val = data[y].max()
            if max_val > 1000:
                # Format with commas for thousands
                data = data.copy()
                if 'total_' in y or 'avg_' in y:
                    # For metrics that are likely counts or averages
                    data[y] = data[y].apply(lambda val: int(val) if val == int(val) else round(val, 1))

        # Limit the number of bars to display to prevent overcrowding
        if len(data) > 10:
            data = data.sort_values(by=y, ascending=False).head(10)

        # Create the bar chart
        fig = px.bar(
            data,
            x=x,
            y=y,
            title=title,
            color=x if len(data[x].unique()) <= 10 else None,
            labels={
                x: x.replace("_", " ").title(),
                y: y.replace("_", " ").title().replace("Avg ", "Average ")
            },
            template="none",
            # Use responsive dimensions instead of fixed values
            height=None,  # Let the container determine the height
            width=None,   # Let the container determine the width
            text_auto=True  # Add value labels on bars
        )

        # Adjust text position on bars
        fig.update_traces(
            textposition='outside',  # Position text outside of bars
            textfont=dict(size=9)   # Smaller text size
        )

        # Apply common layout settings
        fig.update_layout(**self._get_layout(title))

        # Refine the appearance for DB aesthetic
        fig.update_traces(
            marker_line_width=0,
            opacity=0.9,
            hoverinfo="x+y+name",
            hovertemplate="<b>%{x}</b><br><b>" + y.replace("_", " ").title().replace("Avg ", "Average ") + ":</b> %{y:,.1f}"  # Enhanced hover template
        )

        # Add hover effects
        fig.update_layout(
            hoverlabel=dict(
                bgcolor=self.colors["card_bg"],
                font_size=12,
                font_family="Arial",
                font_color=self.colors["text"],
                bordercolor=self.colors["grid"],
                namelength=-1  # Show the full name
            )
        )

        # Ensure clean axis lines and add axis titles
        fig.update_xaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True,  # Prevent x-axis zooming/panning
            title={
                "text": x.replace("_", " ").title(),
                "font": {"size": 12, "color": self.colors["text"]},
                "standoff": 5  # Minimal standoff
            },
            tickfont={"size": 10},  # Smaller font for tick labels
            tickangle=-45,  # Angle the x-axis labels for better readability
            ticks="outside",  # Position ticks outside
            ticklen=5,  # Length of tick marks
            tickwidth=1,  # Width of tick marks
            tickcolor=self.colors["grid"],  # Color of tick marks
            tickmode="auto",  # Auto-select appropriate number of ticks
            nticks=10,  # Maximum number of ticks
            tickformat=".0f" if data[x].dtype.kind in 'ifc' else None  # Format numeric ticks
        )
        fig.update_yaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True,  # Prevent y-axis zooming/panning
            title={
                "text": y.replace("_", " ").title().replace("Avg ", "Average "),
                "font": {"size": 12, "color": self.colors["text"]},
                "standoff": 5  # Minimal standoff
            }
        )

        # Disable all interactions
        fig.update_layout(dragmode=False)

        return fig

    def _create_line_chart(self, data, x, y, title):
        """Create a line chart with DB-inspired styling."""
        # Format y values for better readability if they're numeric
        if y in data.columns and data[y].dtype.kind in 'ifc':
            # Make a copy to avoid modifying the original
            data = data.copy()
            # Round numeric values to 2 decimal places
            data[y] = data[y].apply(lambda val: round(val, 2))

        # Remove title from the figure as we'll add it separately in the layout
        fig = px.line(
            data,
            x=x,
            y=y,
            title=None,  # Remove title from the figure
            markers=True,
            labels={
                x: x.replace("_", " ").title(),
                y: y.replace("_", " ").title()
            },
            template="none",
            # Use responsive dimensions instead of fixed values
            height=None,  # Let the container determine the height
            width=None    # Let the container determine the width
        )

        # Get layout settings but remove the title
        layout_settings = self._get_layout("")

        # Apply layout settings
        fig.update_layout(**layout_settings)

        # Refine the appearance
        fig.update_traces(
            marker_size=8,
            line_width=3,
            hovertemplate="<b>%{x}</b><br><b>" + y.replace("_", " ").title().replace("Avg ", "Average ") + ":</b> %{y:.2f}"
        )

        # Add hover effects
        fig.update_layout(
            hoverlabel=dict(
                bgcolor=self.colors["card_bg"],
                font_size=12,
                font_family="Arial",
                font_color=self.colors["text"],
                bordercolor=self.colors["grid"],
                namelength=-1  # Show the full name
            )
        )

        # Ensure clean axis lines
        fig.update_xaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True,  # Prevent x-axis zooming/panning
            title={
                "text": x.replace("_", " ").title(),
                "font": {"size": 12, "color": self.colors["text"]},
                "standoff": 10  # Add some space between the axis and the title
            },
            tickangle=-45,  # Angle the x-axis labels for better readability
            tickfont={"size": 10},  # Smaller font for tick labels
            tickmode="auto",  # Auto-select appropriate number of ticks
            nticks=10,  # Maximum number of ticks
            tickformat=".0f" if data[x].dtype.kind in 'ifc' else None  # Format numeric ticks
        )
        fig.update_yaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True,  # Prevent y-axis zooming/panning
            title=y.replace("_", " ").title()  # Add y-axis title
        )

        # Disable all interactions and annotations
        fig.update_layout(
            dragmode=False,
            showlegend=False,  # Hide legend if not needed
            annotations=[]  # Remove any annotations
        )

        return fig

    def _create_pie_chart(self, data, x, y, title):
        """Create a pie chart with DB-inspired styling."""
        fig = px.pie(
            data,
            names=x,
            values=y,
            title=title,
            labels={
                x: x.replace("_", " ").title(),
                y: y.replace("_", " ").title()
            },
            template="none",
            # Use responsive dimensions instead of fixed values
            height=None,  # Let the container determine the height
            width=None    # Let the container determine the width
        )

        # Apply common layout settings
        layout = self._get_layout(title)
        fig.update_layout(**layout)

        # Adjust hole size for donut chart
        fig.update_traces(
            hole=0.4,
            marker=dict(line=dict(color=self.colors["bg"], width=1)),
            hoverinfo="label+percent",
            textinfo="label+percent",
            textfont_size=12,
            hovertemplate="<b>%{label}</b><br><b>" + y.replace("_", " ").title().replace("Avg ", "Average ") + ":</b> %{value:.2f}<br><b>Percentage:</b> %{percent}"
        )

        # Add hover effects
        fig.update_layout(
            hoverlabel=dict(
                bgcolor=self.colors["card_bg"],
                font_size=12,
                font_family="Arial",
                font_color=self.colors["text"],
                bordercolor=self.colors["grid"],
                namelength=-1  # Show the full name
            )
        )

        # Disable all interactions
        fig.update_layout(dragmode=False)

        return fig

    def _create_scatter_chart(self, data, x, y, title):
        """Create a scatter chart with DB-inspired styling."""
        fig = px.scatter(
            data,
            x=x,
            y=y,
            title=title,
            size=data[y] if y in data.columns else None,
            color=x if len(data[x].unique()) <= 10 else None,
            labels={
                x: x.replace("_", " ").title(),
                y: y.replace("_", " ").title()
            },
            template="none",
            # Use responsive dimensions instead of fixed values
            height=None,  # Let the container determine the height
            width=None    # Let the container determine the width
        )

        # Apply common layout settings
        fig.update_layout(**self._get_layout(title))

        # Refine the appearance
        fig.update_traces(
            marker=dict(
                line=dict(width=1, color=self.colors["bg"]),
                opacity=0.8
            ),
            hovertemplate="<b>%{x}</b><br>%{y:.2f}"
        )

        # Ensure clean axis lines
        fig.update_xaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True  # Prevent x-axis zooming/panning
        )
        fig.update_yaxes(
            showline=True,
            linewidth=1,
            linecolor=self.colors["grid"],
            mirror=False,
            fixedrange=True  # Prevent y-axis zooming/panning
        )

        # Disable all interactions
        fig.update_layout(dragmode=False)

        return fig

    def _create_empty_chart(self, title):
        """Create an empty chart with a message."""
        fig = go.Figure()

        fig.add_annotation(
            text="No data available",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(
                family="Inter, Arial, sans-serif",
                size=16,
                color=self.colors["text"]
            )
        )

        # Apply common layout settings
        fig.update_layout(
            **self._get_layout(title),
            # Use responsive dimensions instead of fixed values
            height=None,  # Let the container determine the height
            width=None,   # Let the container determine the width
            dragmode=False  # Disable all interactions
        )

        return fig
