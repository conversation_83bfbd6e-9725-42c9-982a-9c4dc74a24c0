"""
Platform Metrics Mapping for Euranet Dashboard.
This component provides a comprehensive mapping of platforms and their associated metrics.
"""

class PlatformMetrics:
    """Provides mapping of platforms and their associated metrics."""

    def __init__(self):
        """Initialize the platform metrics mapping."""
        # Platform categories
        self.categories = {
            "podcast": ["Spotify", "ApplePodcast", "SoundCloud", "GooglePodcast", "Podbean", "CustomPlatform", "Unknown"],
            "social": ["Facebook", "Twitter/X", "Instagram", "Youtube", "LinkedIn", "Tiktok"],
            "web": ["URL", "Website"]
        }

        # Podcast types (from actual database)
        self.podcast_types = [
            "Gen Z", "GEN Z", "GenZ", "Genz", "Gen-Z",  # Gen Z variations
            "Green Deal", "Compromisso Verde/Green Deal",  # Green Deal variations
            "EUROPE", "Europa Europa", "Europa sostenibile", "Euroopa podcast", "Euroopa Täna", "See on Euroopa",  # Europe variations
            "EU und Du", "EU und DU", "EU Und Du",  # EU und Du variations
            "Can EU do this", "Can EU do this?", "Can EU Do That", "Chiedilo all'Europa.Can the EU do this?",  # Can EU do this variations
            "I live in Europe",
            "Radar Europa/What can EU do...",
            "Visto de Fora",
            "Casa Comun",
            "Panorama", "Euranet Plus Panorama",
            "Voices from Brussels", "Voices from Brussels - FR", "Voices from Brussels - french",
            "Other", "Others"
        ]

        # Podcast type categories (grouping similar types)
        self.podcast_type_categories = {
            "gen_z": ["Gen Z", "GEN Z", "GenZ", "Genz", "Gen-Z"],
            "green_deal": ["Green Deal", "Compromisso Verde/Green Deal"],
            "europe": ["EUROPE", "Europa Europa", "Europa sostenibile", "Euroopa podcast", "Euroopa Täna", "See on Euroopa"],
            "eu_und_du": ["EU und Du", "EU und DU", "EU Und Du"],
            "can_eu_do_this": ["Can EU do this", "Can EU do this?", "Can EU Do That", "Chiedilo all'Europa.Can the EU do this?"],
            "voices_from_brussels": ["Voices from Brussels", "Voices from Brussels - FR", "Voices from Brussels - french"],
            "other": ["Other", "Others", ""]
        }

        # Metrics for each platform category
        self.category_metrics = {
            "podcast": {
                "primary": ["plays", "listeners"],
                "secondary": ["starts", "streams", "likes", "comments"],
                "calculated": []
            },
            "social": {
                "primary": ["reach", "impressions", "engagements"],
                "secondary": ["likes", "comments", "shares", "video_views", "interactions"],
                "calculated": ["engagement_rate"]
            },
            "web": {
                "primary": ["unique_visitors", "visits", "page_views"],
                "secondary": [],
                "calculated": []
            }
        }

        # Platform-specific metrics
        self.platform_metrics = {
            "Spotify": {
                "primary": ["plays", "listeners"],
                "secondary": ["starts", "streams"],
                "calculated": ["completion_rate"]
            },
            "ApplePodcast": {
                "primary": ["plays", "listeners"],
                "secondary": ["starts", "streams"],
                "calculated": []
            },
            "SoundCloud": {
                "primary": ["plays", "listeners"],
                "secondary": ["likes", "comments"],
                "calculated": []
            },
            "GooglePodcast": {
                "primary": ["plays", "listeners"],
                "secondary": ["starts"],
                "calculated": []
            },
            "Podbean": {
                "primary": ["plays", "listeners"],
                "secondary": ["likes", "comments"],
                "calculated": []
            },
            "Facebook": {
                "primary": ["reach", "impressions", "engagements"],
                "secondary": ["likes", "comments", "shares"],
                "calculated": ["engagement_rate"]
            },
            "Twitter/X": {
                "primary": ["impressions", "engagements"],
                "secondary": ["likes", "retweets", "comments"],
                "calculated": ["engagement_rate"]
            },
            "Instagram": {
                "primary": ["reach", "impressions", "engagements"],
                "secondary": ["likes", "comments", "shares"],
                "calculated": ["engagement_rate"]
            },
            "Youtube": {
                "primary": ["video_views", "watch_time"],
                "secondary": ["likes", "comments", "subscribers"],
                "calculated": ["avg_watch_time"]
            },
            "LinkedIn": {
                "primary": ["impressions", "engagements"],
                "secondary": ["likes", "comments", "shares"],
                "calculated": ["engagement_rate"]
            },
            "Tiktok": {
                "primary": ["video_views", "reach"],
                "secondary": ["likes", "comments", "shares"],
                "calculated": ["engagement_rate"]
            },
            "URL": {
                "primary": ["unique_visitors", "visits", "page_views"],
                "secondary": [],
                "calculated": []
            },
            "Website": {
                "primary": ["unique_visitors", "visits", "page_views"],
                "secondary": [],
                "calculated": []
            }
        }

        # Metric display names
        self.metric_display_names = {
            "plays": "Plays",
            "listeners": "Listeners",
            "starts": "Starts",
            "streams": "Streams",
            "likes": "Likes",
            "comments": "Comments",
            "shares": "Shares",
            "reach": "Reach",
            "impressions": "Impressions",
            "engagements": "Engagements",
            "interactions": "Interactions",
            "video_views": "Video Views",
            "watch_time": "Watch Time",
            "subscribers": "Subscribers",
            "unique_visitors": "Unique Visitors",
            "page_views": "Page Views",
            "visits": "Visits",
            "link_clicks": "Link Clicks",
            "retweets": "Retweets",
            "engagement_rate": "Engagement Rate",
            "completion_rate": "Completion Rate",
            "bounce_rate": "Bounce Rate",
            "avg_session_duration": "Avg. Session Duration",
            "avg_watch_time": "Avg. Watch Time"
        }

        # Metric descriptions
        self.metric_descriptions = {
            "plays": "Number of times a podcast episode has been played",
            "listeners": "Unique number of users who listened to a podcast",
            "starts": "Number of times a podcast episode has been started",
            "streams": "Number of times a podcast episode has been streamed",
            "likes": "Number of likes received on content",
            "comments": "Number of comments received on content",
            "shares": "Number of times content has been shared",
            "reach": "Number of unique users who saw the content",
            "impressions": "Total number of times content was displayed",
            "engagements": "Total number of interactions with content",
            "interactions": "Sum of likes, comments, and shares",
            "video_views": "Number of times a video has been viewed",
            "watch_time": "Total time spent watching videos",
            "subscribers": "Number of subscribers to a channel",
            "unique_visitors": "Number of unique visitors to a website",
            "page_views": "Number of pages viewed on a website",
            "visits": "Number of visits to a website",
            "link_clicks": "Number of clicks on links",
            "retweets": "Number of times content has been retweeted",
            "engagement_rate": "Percentage of users who engaged with content",
            "completion_rate": "Percentage of users who completed listening to a podcast",
            "bounce_rate": "Percentage of visitors who navigate away after viewing only one page",
            "avg_session_duration": "Average time spent on a website per session",
            "avg_watch_time": "Average time spent watching a video"
        }

        # Metric calculation methods
        self.metric_calculations = {
            "engagement_rate": lambda data: (data["engagements"] / data["impressions"]) * 100 if "engagements" in data and "impressions" in data and data["impressions"] > 0 else 0,
            "completion_rate": lambda data: (data["plays"] / data["starts"]) * 100 if "plays" in data and "starts" in data and data["starts"] > 0 else 0,
            "bounce_rate": lambda data: 0,  # Placeholder for actual calculation
            "avg_session_duration": lambda data: 0,  # Placeholder for actual calculation
            "avg_watch_time": lambda data: data["watch_time"] / data["video_views"] if "watch_time" in data and "video_views" in data and data["video_views"] > 0 else 0
        }

    def get_platform_category(self, platform):
        """
        Get the category of a platform.

        Args:
            platform: The platform name

        Returns:
            str: The platform category
        """
        for category, platforms in self.categories.items():
            if platform in platforms:
                return category
        return None

    def get_metrics_for_platform(self, platform):
        """
        Get all metrics for a specific platform.

        Args:
            platform: The platform name

        Returns:
            dict: Dictionary of primary, secondary, and calculated metrics
        """
        if platform in self.platform_metrics:
            return self.platform_metrics[platform]

        # If platform not found, try to get metrics for its category
        category = self.get_platform_category(platform)
        if category and category in self.category_metrics:
            return self.category_metrics[category]

        # Default to empty metrics
        return {"primary": [], "secondary": [], "calculated": []}

    def get_metrics_for_category(self, category):
        """
        Get all metrics for a platform category.

        Args:
            category: The platform category

        Returns:
            dict: Dictionary of primary, secondary, and calculated metrics
        """
        if category in self.category_metrics:
            return self.category_metrics[category]

        # Default to empty metrics
        return {"primary": [], "secondary": [], "calculated": []}

    def get_all_metrics_for_platform(self, platform):
        """
        Get a flat list of all metrics for a platform.

        Args:
            platform: The platform name

        Returns:
            list: List of all metrics for the platform
        """
        metrics = self.get_metrics_for_platform(platform)
        return metrics["primary"] + metrics["secondary"] + metrics["calculated"]

    def get_display_name(self, metric):
        """
        Get the display name for a metric.

        Args:
            metric: The metric name

        Returns:
            str: The display name for the metric
        """
        return self.metric_display_names.get(metric, metric.replace("_", " ").title())

    def get_description(self, metric):
        """
        Get the description for a metric.

        Args:
            metric: The metric name

        Returns:
            str: The description for the metric
        """
        return self.metric_descriptions.get(metric, "")

    def calculate_metric(self, metric, data):
        """
        Calculate a derived metric from raw data.

        Args:
            metric: The metric to calculate
            data: Dictionary of raw metrics

        Returns:
            float: The calculated metric value
        """
        if metric in self.metric_calculations:
            return self.metric_calculations[metric](data)
        return 0

    def get_primary_metric_for_platform(self, platform):
        """
        Get the primary metric for a platform.

        Args:
            platform: The platform name

        Returns:
            str: The primary metric for the platform
        """
        metrics = self.get_metrics_for_platform(platform)
        return metrics["primary"][0] if metrics["primary"] else None

    def get_primary_metric_for_category(self, category):
        """
        Get the primary metric for a platform category.

        Args:
            category: The platform category

        Returns:
            str: The primary metric for the category
        """
        metrics = self.get_metrics_for_category(category)
        return metrics["primary"][0] if metrics["primary"] else None

    def get_podcast_types(self):
        """
        Get all podcast types.

        Returns:
            list: List of podcast types
        """
        return self.podcast_types

    def is_podcast_type(self, term):
        """
        Check if a term is a podcast type.

        Args:
            term: The term to check

        Returns:
            bool: True if the term is a podcast type, False otherwise
        """
        return term.lower() in [t.lower() for t in self.podcast_types]

    def get_podcast_type_category(self, podcast_type):
        """
        Get the category of a podcast type.

        Args:
            podcast_type: The podcast type

        Returns:
            str: The podcast type category
        """
        for category, types in self.podcast_type_categories.items():
            if podcast_type in types or podcast_type.lower() in [t.lower() for t in types]:
                return category
        return "other"

    def get_normalized_podcast_type(self, podcast_type):
        """
        Get the normalized version of a podcast type.
        This returns the first podcast type in the category.

        Args:
            podcast_type: The podcast type

        Returns:
            str: The normalized podcast type
        """
        category = self.get_podcast_type_category(podcast_type)
        if category in self.podcast_type_categories and self.podcast_type_categories[category]:
            return self.podcast_type_categories[category][0]
        return podcast_type
