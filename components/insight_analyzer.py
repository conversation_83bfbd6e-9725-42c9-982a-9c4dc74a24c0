"""
Insight Analyzer for Euranet Dashboard.
This component analyzes insights and determines their complexity and relationships.
"""

import re
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from collections import Counter

class InsightAnalyzer:
    """Analyzes insights to determine their complexity and relationships."""

    def __init__(self):
        """Initialize the insight analyzer."""
        self.initialized = False
        try:
            # Download NLTK resources if needed
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            
            self.initialized = True
            print("Insight Analyzer initialized successfully")
        except Exception as e:
            print(f"Error initializing Insight Analyzer: {e}")
            self.initialized = False

    def analyze_insights(self, insights, query=None, results=None):
        """
        Analyze a list of insights to determine their complexity and relationships.
        
        Args:
            insights: List of insight strings
            query: Original user query (optional)
            results: Query results dataframe (optional)
            
        Returns:
            List of dictionaries with insight analysis
        """
        if not self.initialized or not insights:
            return [{"text": insight, "complexity": "medium", "category": "general"} for insight in insights]
        
        analyzed_insights = []
        
        # Extract entities and metrics from query if available
        query_entities = self._extract_entities(query) if query else []
        query_metrics = self._extract_metrics(query) if query else []
        
        for insight in insights:
            # Basic analysis
            complexity = self._determine_complexity(insight)
            category = self._determine_category(insight, query_entities, query_metrics)
            importance = self._determine_importance(insight, query_entities, query_metrics)
            
            # Extract numerical values
            values = self._extract_values(insight)
            
            # Create analyzed insight
            analyzed_insight = {
                "text": insight,
                "complexity": complexity,
                "category": category,
                "importance": importance,
                "values": values
            }
            
            analyzed_insights.append(analyzed_insight)
        
        # Sort insights by importance
        analyzed_insights.sort(key=lambda x: x["importance"], reverse=True)
        
        return analyzed_insights

    def _determine_complexity(self, insight):
        """Determine the complexity of an insight."""
        # Count sentences and words
        sentences = sent_tokenize(insight)
        words = word_tokenize(insight)
        
        # Simple heuristic for complexity
        if len(sentences) > 2 or len(words) > 25:
            return "high"
        elif len(sentences) > 1 or len(words) > 15:
            return "medium"
        else:
            return "low"

    def _determine_category(self, insight, query_entities=None, query_metrics=None):
        """Determine the category of an insight."""
        insight_lower = insight.lower()
        
        # Check for comparison insights
        if any(term in insight_lower for term in ["highest", "lowest", "most", "least", "compared", "versus", "vs"]):
            return "comparison"
        
        # Check for trend insights
        if any(term in insight_lower for term in ["trend", "increase", "decrease", "growth", "decline", "over time"]):
            return "trend"
        
        # Check for statistical insights
        if any(term in insight_lower for term in ["average", "mean", "median", "total", "sum", "percentage", "ratio"]):
            return "statistical"
        
        # Default category
        return "general"

    def _determine_importance(self, insight, query_entities=None, query_metrics=None):
        """Determine the importance of an insight based on relevance to query."""
        importance = 5  # Default medium importance
        
        # Increase importance for insights with query entities
        if query_entities:
            for entity in query_entities:
                if entity.lower() in insight.lower():
                    importance += 2
        
        # Increase importance for insights with query metrics
        if query_metrics:
            for metric in query_metrics:
                if metric.lower() in insight.lower():
                    importance += 2
        
        # Increase importance for insights with numerical values
        values = self._extract_values(insight)
        importance += min(len(values), 3)
        
        # Increase importance for insights with superlatives
        if any(term in insight.lower() for term in ["highest", "lowest", "most", "least", "best", "worst"]):
            importance += 2
        
        # Cap importance at 10
        return min(importance, 10)

    def _extract_entities(self, text):
        """Extract potential entities from text."""
        if not text:
            return []
        
        # Simple entity extraction based on capitalized words
        words = word_tokenize(text)
        entities = [word for word in words if word[0].isupper() and len(word) > 1]
        
        # Add common entities in the domain
        domain_entities = ["podcast", "social media", "Facebook", "Twitter", "Instagram", "YouTube"]
        entities.extend([e for e in domain_entities if e.lower() in text.lower()])
        
        return list(set(entities))

    def _extract_metrics(self, text):
        """Extract potential metrics from text."""
        if not text:
            return []
        
        metrics = []
        metric_terms = ["plays", "downloads", "likes", "shares", "comments", "reach", "engagement", 
                       "views", "visitors", "unique visitors"]
        
        for metric in metric_terms:
            if metric in text.lower():
                metrics.append(metric)
        
        return metrics

    def _extract_values(self, text):
        """Extract numerical values from text."""
        # Find numbers with optional commas and optional decimal points
        number_pattern = r'(?:[\d,]+\.?\d*|\.\d+)'
        
        # Find percentages
        percentage_pattern = f'{number_pattern}\\s*%'
        
        # Find numbers with K, M, B suffixes (e.g., 1.5K, 2M)
        suffix_pattern = f'{number_pattern}\\s*[KMB]\\b'
        
        # Combine patterns
        combined_pattern = f'({percentage_pattern}|{suffix_pattern}|{number_pattern})'
        
        # Find all matches
        matches = re.findall(combined_pattern, text)
        
        return matches
