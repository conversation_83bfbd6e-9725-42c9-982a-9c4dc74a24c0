"""
Multi-Agent Insight Generation for Euranet Dashboard.
This component implements a system of specialized agents for generating insights.
"""

import json
import time
import pandas as pd
from abc import ABC, abstractmethod
from components.gemma_model import GemmaModel
from components.platform_metrics import PlatformMetrics
from components.metric_calculator import MetricCalculator

class InsightAgent(ABC):
    """Abstract base class for insight agents."""
    
    def __init__(self, name, description):
        """
        Initialize the insight agent.
        
        Args:
            name (str): The name of the agent.
            description (str): A description of the agent's purpose.
        """
        self.name = name
        self.description = description
        self.memory = []
        self.created_at = time.time()
        self.last_used = time.time()
        
    @abstractmethod
    def generate_insights(self, query, data, context=None):
        """
        Generate insights from data.
        
        Args:
            query (str): The original query.
            data (DataFrame): The data to analyze.
            context (dict, optional): Additional context information.
            
        Returns:
            list: A list of insights.
        """
        pass
        
    def add_to_memory(self, item):
        """
        Add an item to the agent's memory.
        
        Args:
            item (dict): The memory item to add.
        """
        # Add timestamp to the memory item
        item['timestamp'] = time.time()
        self.memory.append(item)
        
        # Update last used timestamp
        self.last_used = time.time()
        
        # Limit memory size to prevent unbounded growth
        if len(self.memory) > 100:
            self.memory = self.memory[-100:]
            
    def get_relevant_memories(self, query, limit=5):
        """
        Get memories relevant to the current query.
        
        Args:
            query (str): The current query.
            limit (int): Maximum number of memories to return.
            
        Returns:
            list: A list of relevant memories.
        """
        # Simple relevance scoring based on keyword matching
        scored_memories = []
        query_words = set(query.lower().split())
        
        for memory in self.memory:
            # Skip memories without content
            if 'content' not in memory:
                continue
                
            # Calculate relevance score
            memory_words = set(memory['content'].lower().split())
            common_words = query_words.intersection(memory_words)
            score = len(common_words) / max(len(query_words), 1)
            
            # Add time decay factor (more recent memories get higher scores)
            time_factor = 1.0 - min(1.0, (time.time() - memory['timestamp']) / (7 * 24 * 3600))  # 1 week decay
            final_score = score * (0.7 + 0.3 * time_factor)
            
            scored_memories.append((memory, final_score))
            
        # Sort by score and return top memories
        scored_memories.sort(key=lambda x: x[1], reverse=True)
        return [memory for memory, score in scored_memories[:limit]]


class TrendAgent(InsightAgent):
    """Agent specialized in identifying trends in data."""
    
    def __init__(self, gemma_model=None):
        """
        Initialize the trend agent.
        
        Args:
            gemma_model (GemmaModel, optional): The Gemma model for enhanced insights.
        """
        super().__init__("TrendAgent", "Identifies trends and patterns over time")
        self.gemma_model = gemma_model
        
    def generate_insights(self, query, data, context=None):
        """
        Generate trend-related insights from data.
        
        Args:
            query (str): The original query.
            data (DataFrame): The data to analyze.
            context (dict, optional): Additional context information.
            
        Returns:
            list: A list of trend insights.
        """
        insights = []
        context = context or {}
        
        # Check if we have time-based data
        if data.empty:
            return ["No data available for trend analysis."]
            
        # Check for date/time columns
        date_columns = [col for col in data.columns if 'date' in col.lower() or 'time' in col.lower()]
        
        if not date_columns:
            return ["No time-based data available for trend analysis."]
            
        # Use the first date column for analysis
        date_col = date_columns[0]
        
        # Try to convert to datetime if not already
        try:
            if data[date_col].dtype != 'datetime64[ns]':
                data[date_col] = pd.to_datetime(data[date_col])
        except Exception as e:
            print(f"Error converting date column: {e}")
            
        # Get numeric columns for trend analysis
        numeric_cols = [col for col in data.columns if data[col].dtype.kind in 'ifc' and col != date_col]
        
        if not numeric_cols:
            return ["No numeric data available for trend analysis."]
            
        # Sort data by date
        try:
            data = data.sort_values(by=date_col)
        except Exception as e:
            print(f"Error sorting data by date: {e}")
            
        # Check for trends in each numeric column
        for col in numeric_cols:
            try:
                # Get first and last values
                first_val = data[col].iloc[0]
                last_val = data[col].iloc[-1]
                
                # Calculate change
                change = last_val - first_val
                pct_change = (change / first_val * 100) if first_val != 0 else float('inf')
                
                # Generate insight based on change
                if abs(pct_change) > 20:
                    direction = "increased" if change > 0 else "decreased"
                    insight = f"{col} has {direction} by {abs(pct_change):.1f}% over the period."
                    insights.append(insight)
                    
                    # Add to memory
                    self.add_to_memory({
                        'type': 'trend',
                        'content': insight,
                        'metric': col,
                        'change': pct_change
                    })
            except Exception as e:
                print(f"Error analyzing trend for {col}: {e}")
                
        # Use Gemma for enhanced insights if available
        if self.gemma_model and len(insights) < 3:
            try:
                # Create a trend-specific prompt
                prompt = f"""
                Analyze the following time-series data for trends and patterns:
                
                Query: {query}
                
                Data:
                {data.head(20).to_string()}
                
                Focus specifically on:
                1. Long-term trends (increasing, decreasing, stable)
                2. Seasonal patterns or cyclical behavior
                3. Acceleration or deceleration in trends
                4. Anomalies or outliers in the time series
                
                Generate 2-3 specific, data-driven insights about trends in this data.
                """
                
                # Get relevant memories to provide context
                memories = self.get_relevant_memories(query)
                if memories:
                    prompt += "\n\nPrevious observations:\n"
                    for memory in memories:
                        prompt += f"- {memory['content']}\n"
                
                # Generate insights
                gemma_response = self.gemma_model.generate_response(prompt)
                
                # Parse insights (one per line, filtering out short lines)
                gemma_insights = [line.strip() for line in gemma_response.split('\n') 
                                 if line.strip() and len(line.strip()) > 20]
                
                # Add Gemma insights
                for insight in gemma_insights[:3]:  # Limit to 3 insights
                    if insight not in insights:
                        insights.append(insight)
                        
                        # Add to memory
                        self.add_to_memory({
                            'type': 'trend',
                            'content': insight,
                            'source': 'gemma'
                        })
            except Exception as e:
                print(f"Error generating enhanced trend insights: {e}")
                
        # Ensure we have at least one insight
        if not insights:
            insights = ["No significant trends detected in the data."]
            
        return insights


class ComparisonAgent(InsightAgent):
    """Agent specialized in comparing different entities in data."""
    
    def __init__(self, gemma_model=None):
        """
        Initialize the comparison agent.
        
        Args:
            gemma_model (GemmaModel, optional): The Gemma model for enhanced insights.
        """
        super().__init__("ComparisonAgent", "Compares different entities and identifies differences")
        self.gemma_model = gemma_model
        
    def generate_insights(self, query, data, context=None):
        """
        Generate comparison-related insights from data.
        
        Args:
            query (str): The original query.
            data (DataFrame): The data to analyze.
            context (dict, optional): Additional context information.
            
        Returns:
            list: A list of comparison insights.
        """
        insights = []
        context = context or {}
        
        if data.empty:
            return ["No data available for comparison analysis."]
            
        # Identify potential category columns for comparison
        # Look for columns with few unique values relative to row count
        category_cols = []
        for col in data.columns:
            if data[col].dtype.kind not in 'ifc':  # Not numeric
                unique_ratio = data[col].nunique() / len(data)
                if 0 < unique_ratio < 0.5:  # Less than 50% unique values
                    category_cols.append(col)
                    
        if not category_cols:
            return ["No suitable categories found for comparison analysis."]
            
        # Get numeric columns for comparison
        numeric_cols = [col for col in data.columns if data[col].dtype.kind in 'ifc']
        
        if not numeric_cols:
            return ["No numeric data available for comparison analysis."]
            
        # Perform comparisons for each category column
        for cat_col in category_cols[:2]:  # Limit to 2 category columns
            try:
                # Get top categories by frequency
                top_categories = data[cat_col].value_counts().head(5).index.tolist()
                
                if len(top_categories) < 2:
                    continue
                    
                # Compare top categories for each numeric column
                for num_col in numeric_cols[:3]:  # Limit to 3 numeric columns
                    # Calculate average for each category
                    category_avgs = {}
                    for category in top_categories:
                        category_data = data[data[cat_col] == category]
                        if not category_data.empty:
                            category_avgs[category] = category_data[num_col].mean()
                            
                    if len(category_avgs) < 2:
                        continue
                        
                    # Find highest and lowest
                    highest = max(category_avgs.items(), key=lambda x: x[1])
                    lowest = min(category_avgs.items(), key=lambda x: x[1])
                    
                    # Calculate difference
                    diff_pct = ((highest[1] - lowest[1]) / lowest[1] * 100) if lowest[1] != 0 else float('inf')
                    
                    # Generate insight if difference is significant
                    if diff_pct > 15:
                        insight = f"{highest[0]} has {diff_pct:.1f}% higher {num_col} compared to {lowest[0]}."
                        insights.append(insight)
                        
                        # Add to memory
                        self.add_to_memory({
                            'type': 'comparison',
                            'content': insight,
                            'category': cat_col,
                            'metric': num_col,
                            'entities': [highest[0], lowest[0]],
                            'difference': diff_pct
                        })
            except Exception as e:
                print(f"Error in comparison analysis for {cat_col}: {e}")
                
        # Use Gemma for enhanced insights if available
        if self.gemma_model and len(insights) < 3:
            try:
                # Create a comparison-specific prompt
                prompt = f"""
                Analyze the following data to compare different entities:
                
                Query: {query}
                
                Data:
                {data.head(20).to_string()}
                
                Focus specifically on:
                1. Significant differences between entities
                2. Relative performance rankings
                3. Outliers among the entities
                4. Patterns of similarity or difference
                
                Generate 2-3 specific, data-driven insights comparing different entities in this data.
                """
                
                # Get relevant memories to provide context
                memories = self.get_relevant_memories(query)
                if memories:
                    prompt += "\n\nPrevious observations:\n"
                    for memory in memories:
                        prompt += f"- {memory['content']}\n"
                
                # Generate insights
                gemma_response = self.gemma_model.generate_response(prompt)
                
                # Parse insights (one per line, filtering out short lines)
                gemma_insights = [line.strip() for line in gemma_response.split('\n') 
                                 if line.strip() and len(line.strip()) > 20]
                
                # Add Gemma insights
                for insight in gemma_insights[:3]:  # Limit to 3 insights
                    if insight not in insights:
                        insights.append(insight)
                        
                        # Add to memory
                        self.add_to_memory({
                            'type': 'comparison',
                            'content': insight,
                            'source': 'gemma'
                        })
            except Exception as e:
                print(f"Error generating enhanced comparison insights: {e}")
                
        # Ensure we have at least one insight
        if not insights:
            insights = ["No significant differences detected between entities."]
            
        return insights


class AnomalyAgent(InsightAgent):
    """Agent specialized in detecting anomalies and outliers in data."""
    
    def __init__(self, gemma_model=None):
        """
        Initialize the anomaly agent.
        
        Args:
            gemma_model (GemmaModel, optional): The Gemma model for enhanced insights.
        """
        super().__init__("AnomalyAgent", "Detects anomalies and outliers in data")
        self.gemma_model = gemma_model
        
    def generate_insights(self, query, data, context=None):
        """
        Generate anomaly-related insights from data.
        
        Args:
            query (str): The original query.
            data (DataFrame): The data to analyze.
            context (dict, optional): Additional context information.
            
        Returns:
            list: A list of anomaly insights.
        """
        insights = []
        context = context or {}
        
        if data.empty:
            return ["No data available for anomaly detection."]
            
        # Get numeric columns for anomaly detection
        numeric_cols = [col for col in data.columns if data[col].dtype.kind in 'ifc']
        
        if not numeric_cols:
            return ["No numeric data available for anomaly detection."]
            
        # Detect outliers in each numeric column
        for col in numeric_cols:
            try:
                # Calculate statistics
                mean = data[col].mean()
                std = data[col].std()
                
                # Skip if standard deviation is zero or NaN
                if std == 0 or pd.isna(std):
                    continue
                    
                # Define outliers as values more than 2.5 standard deviations from the mean
                lower_bound = mean - 2.5 * std
                upper_bound = mean + 2.5 * std
                
                # Find outliers
                outliers = data[(data[col] < lower_bound) | (data[col] > upper_bound)]
                
                # Generate insight if outliers exist
                if not outliers.empty:
                    # Get the most extreme outlier
                    if outliers[col].max() - mean > mean - outliers[col].min():
                        extreme = outliers[col].idxmax()
                        direction = "high"
                        value = outliers.loc[extreme, col]
                        z_score = (value - mean) / std
                    else:
                        extreme = outliers[col].idxmin()
                        direction = "low"
                        value = outliers.loc[extreme, col]
                        z_score = (mean - value) / std
                        
                    # Try to get identifying information for the outlier
                    identifier = None
                    for id_col in data.columns:
                        if id_col != col and data[id_col].dtype.kind not in 'ifc':
                            identifier = f"{id_col}: {outliers.loc[extreme, id_col]}"
                            break
                            
                    # Generate insight
                    if identifier:
                        insight = f"Detected an unusually {direction} {col} ({value:.2f}) for {identifier}, {z_score:.1f} standard deviations from the mean."
                    else:
                        insight = f"Detected an unusually {direction} {col} value ({value:.2f}), {z_score:.1f} standard deviations from the mean."
                        
                    insights.append(insight)
                    
                    # Add to memory
                    self.add_to_memory({
                        'type': 'anomaly',
                        'content': insight,
                        'metric': col,
                        'z_score': float(z_score),
                        'identifier': identifier
                    })
            except Exception as e:
                print(f"Error in anomaly detection for {col}: {e}")
                
        # Use Gemma for enhanced insights if available
        if self.gemma_model and len(insights) < 2:
            try:
                # Create an anomaly-specific prompt
                prompt = f"""
                Analyze the following data to detect anomalies and outliers:
                
                Query: {query}
                
                Data:
                {data.head(20).to_string()}
                
                Focus specifically on:
                1. Unusual values that deviate significantly from the norm
                2. Unexpected patterns or relationships
                3. Data points that don't follow the general trend
                4. Potential errors or inconsistencies in the data
                
                Generate 1-2 specific, data-driven insights about anomalies in this data.
                """
                
                # Get relevant memories to provide context
                memories = self.get_relevant_memories(query)
                if memories:
                    prompt += "\n\nPrevious observations:\n"
                    for memory in memories:
                        prompt += f"- {memory['content']}\n"
                
                # Generate insights
                gemma_response = self.gemma_model.generate_response(prompt)
                
                # Parse insights (one per line, filtering out short lines)
                gemma_insights = [line.strip() for line in gemma_response.split('\n') 
                                 if line.strip() and len(line.strip()) > 20]
                
                # Add Gemma insights
                for insight in gemma_insights[:2]:  # Limit to 2 insights
                    if insight not in insights:
                        insights.append(insight)
                        
                        # Add to memory
                        self.add_to_memory({
                            'type': 'anomaly',
                            'content': insight,
                            'source': 'gemma'
                        })
            except Exception as e:
                print(f"Error generating enhanced anomaly insights: {e}")
                
        # Ensure we have at least one insight
        if not insights:
            insights = ["No significant anomalies detected in the data."]
            
        return insights


class MultiAgentSystem:
    """Coordinates multiple specialized agents for insight generation."""
    
    def __init__(self, gemma_model=None):
        """
        Initialize the multi-agent system.
        
        Args:
            gemma_model (GemmaModel, optional): The Gemma model for enhanced insights.
        """
        self.gemma_model = gemma_model
        
        # Initialize specialized agents
        self.agents = {
            'trend': TrendAgent(gemma_model),
            'comparison': ComparisonAgent(gemma_model),
            'anomaly': AnomalyAgent(gemma_model)
        }
        
        # Initialize memory for the system
        self.memory = []
        
    def generate_insights(self, query, data, context=None):
        """
        Generate insights using multiple specialized agents.
        
        Args:
            query (str): The original query.
            data (DataFrame): The data to analyze.
            context (dict, optional): Additional context information.
            
        Returns:
            list: A list of insights from all agents.
        """
        all_insights = []
        context = context or {}
        
        # Determine which agents to use based on the query and context
        agents_to_use = self._select_agents(query, context)
        
        # Generate insights from each selected agent
        for agent_type in agents_to_use:
            if agent_type in self.agents:
                agent = self.agents[agent_type]
                try:
                    # Generate insights from this agent
                    agent_insights = agent.generate_insights(query, data, context)
                    
                    # Add agent name as prefix to insights
                    prefixed_insights = [f"{agent.name}: {insight}" for insight in agent_insights]
                    
                    # Add to overall insights
                    all_insights.extend(prefixed_insights)
                    
                    # Add to system memory
                    self._add_to_memory({
                        'type': 'agent_insights',
                        'agent': agent_type,
                        'query': query,
                        'insights': agent_insights,
                        'timestamp': time.time()
                    })
                except Exception as e:
                    print(f"Error generating insights with {agent_type} agent: {e}")
        
        # Use Gemma to synthesize insights if available
        if self.gemma_model and len(all_insights) > 3:
            try:
                synthesized_insights = self._synthesize_insights(all_insights, query, data)
                return synthesized_insights
            except Exception as e:
                print(f"Error synthesizing insights: {e}")
                
        return all_insights
        
    def _select_agents(self, query, context):
        """
        Select which agents to use based on the query and context.
        
        Args:
            query (str): The original query.
            context (dict): Additional context information.
            
        Returns:
            list: A list of agent types to use.
        """
        query_lower = query.lower()
        selected_agents = []
        
        # Check for trend-related keywords
        trend_keywords = ['trend', 'over time', 'growth', 'increase', 'decrease', 
                          'change', 'evolution', 'history', 'development']
        if any(keyword in query_lower for keyword in trend_keywords):
            selected_agents.append('trend')
            
        # Check for comparison-related keywords
        comparison_keywords = ['compare', 'comparison', 'versus', 'vs', 'difference',
                              'better', 'worse', 'higher', 'lower', 'between']
        if any(keyword in query_lower for keyword in comparison_keywords):
            selected_agents.append('comparison')
            
        # Check for anomaly-related keywords
        anomaly_keywords = ['anomaly', 'outlier', 'unusual', 'unexpected', 'strange',
                           'odd', 'abnormal', 'irregular', 'deviation']
        if any(keyword in query_lower for keyword in anomaly_keywords):
            selected_agents.append('anomaly')
            
        # If no specific agents were selected, use all of them
        if not selected_agents:
            selected_agents = list(self.agents.keys())
            
        return selected_agents
        
    def _add_to_memory(self, item):
        """
        Add an item to the system memory.
        
        Args:
            item (dict): The memory item to add.
        """
        self.memory.append(item)
        
        # Limit memory size to prevent unbounded growth
        if len(self.memory) > 100:
            self.memory = self.memory[-100:]
            
    def _synthesize_insights(self, all_insights, query, data):
        """
        Use Gemma to synthesize insights from multiple agents.
        
        Args:
            all_insights (list): Insights from all agents.
            query (str): The original query.
            data (DataFrame): The data being analyzed.
            
        Returns:
            list: A list of synthesized insights.
        """
        # Create a synthesis prompt
        prompt = f"""
        You are an AI assistant tasked with synthesizing insights from multiple specialized agents.
        
        Original Query: "{query}"
        
        Insights from specialized agents:
        {json.dumps(all_insights, indent=2)}
        
        Please synthesize these insights into 3-5 key takeaways that:
        1. Eliminate redundancy across agents
        2. Prioritize the most important findings
        3. Connect related insights from different agents
        4. Present a coherent narrative about the data
        
        Format each synthesized insight as a separate point.
        """
        
        # Generate synthesized insights
        response = self.gemma_model.generate_response(prompt)
        
        # Parse insights (one per line, filtering out short lines)
        synthesized_insights = [line.strip() for line in response.split('\n') 
                               if line.strip() and len(line.strip()) > 20]
        
        # Ensure we have at least some insights
        if not synthesized_insights:
            return all_insights[:5]  # Return top 5 original insights
            
        return synthesized_insights[:5]  # Return top 5 synthesized insights
