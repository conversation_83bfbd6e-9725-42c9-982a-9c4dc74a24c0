"""
Suggestion generator for Euranet Chatbot.
Provides contextually relevant suggestions based on chat history and results.
"""
import re

class SuggestionGenerator:
    """Generates contextually relevant suggestions for the chatbot."""
    
    def __init__(self, db_connector):
        self.db = db_connector
    
    def generate_suggestions(self, chat_history, current_results=None):
        """Generate suggestions based on chat history and current results."""
        # Default suggestions
        base_suggestions = [
            "Show me the top 5 stations by podcast plays",
            "Compare Facebook and Instagram performance",
            "What are the trends in podcast listening over time?",
            "Which country has the highest social media engagement?",
            "Show me the most popular platforms for each country"
        ]
        
        # If we have no history or results, return base suggestions
        if not chat_history or len(chat_history) == 0:
            return base_suggestions
        
        context_suggestions = []
        
        # Get the most recent query
        last_interaction = chat_history[-1]
        last_query = last_interaction["query"] if "query" in last_interaction else ""
        
        # Add follow-up suggestions based on the last query
        if "top" in last_query.lower() or "best" in last_query.lower():
            # If they asked for top stations, suggest comparing or trend analysis
            if "station" in last_query.lower():
                context_suggestions.append("How have these stations performed over time?")
                context_suggestions.append("Compare these stations across different platforms")
            
            # If they asked about a specific platform, suggest other platforms
            for platform in ["Facebook", "Instagram", "Twitter", "YouTube", "Spotify", "Apple"]:
                if platform.lower() in last_query.lower():
                    other_platforms = [p for p in ["Facebook", "Instagram", "Twitter", "YouTube"] 
                                     if p.lower() not in last_query.lower()]
                    if other_platforms:
                        context_suggestions.append(f"Compare with {other_platforms[0]} performance")
        
        # If they asked about comparison, suggest trend analysis
        if "compare" in last_query.lower() or "vs" in last_query.lower():
            context_suggestions.append("How has this comparison changed over time?")
            
            # Extract entities that were compared
            platform_pattern = r'\b(Facebook|Instagram|Twitter|X|YouTube|Spotify|Apple)\b'
            platforms = re.findall(platform_pattern, last_query, re.IGNORECASE)
            
            if platforms:
                for platform in platforms:
                    context_suggestions.append(f"What are the top stations on {platform}?")
        
        # If they asked about trends, suggest comparison
        if "trend" in last_query.lower() or "over time" in last_query.lower():
            context_suggestions.append("How do different platforms compare to each other?")
            
            # If trend query specified a metric, suggest other metrics
            metrics = {
                "play": "likes",
                "like": "shares",
                "share": "comments",
                "comment": "plays",
                "reach": "engagement"
            }
            
            for key, value in metrics.items():
                if key in last_query.lower():
                    context_suggestions.append(f"Show me trends for {value} instead")
                    break
        
        # Add suggestions based on current results if available
        if current_results is not None and not current_results.empty:
            # If we have station data, suggest exploring specific stations
            if "station" in current_results.columns:
                top_station = current_results["station"].iloc[0]
                context_suggestions.append(f"Tell me more about {top_station}")
                context_suggestions.append(f"What platforms perform best for {top_station}?")
            
            # If we have country data, suggest exploring specific countries
            if "country" in current_results.columns:
                top_country = current_results["country"].iloc[0]
                context_suggestions.append(f"How do stations in {top_country} compare?")
                context_suggestions.append(f"What's the trend for {top_country} over time?")
            
            # If we have platform data, suggest exploring that platform
            if "platform" in current_results.columns:
                top_platform = current_results["platform"].iloc[0]
                context_suggestions.append(f"Which stations perform best on {top_platform}?")
                context_suggestions.append(f"How has {top_platform} performance changed over time?")
        
        # Combine and prioritize suggestions
        all_suggestions = context_suggestions + base_suggestions
        
        # Return unique suggestions (up to 5)
        unique_suggestions = []
        for suggestion in all_suggestions:
            if suggestion not in unique_suggestions and len(unique_suggestions) < 5:
                unique_suggestions.append(suggestion)
        
        return unique_suggestions
