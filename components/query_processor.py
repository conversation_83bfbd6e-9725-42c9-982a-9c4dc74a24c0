"""
Query processor for Euranet Chatbot.
Converts natural language queries into SQL and processes results.
"""
import re
import pandas as pd
import os
from components.gemma_model import GemmaModel
from components.adk_integration import ADKIntegration
from components.platform_metrics import PlatformMetrics
from components.metric_calculator import MetricCalculator
from components.insight_agents import MultiAgentSystem

class QueryProcessor:
    """Processes natural language queries into SQL."""

    def __init__(self, db_connector, use_gemma=True, use_adk=True):
        self.db = db_connector
        self.table_info = db_connector.get_table_info()
        self.use_gemma = use_gemma
        self.use_adk = use_adk

        # Initialize platform metrics mapping
        self.platform_metrics = PlatformMetrics()

        # Initialize metric calculator
        self.metric_calculator = MetricCalculator()

        # Initialize multi-agent system
        self.multi_agent_system = None

        # Initialize Gemma model if enabled
        self.gemma_model = None
        if use_gemma:
            try:
                # Check for API key in environment variables
                api_key = os.environ.get('GEMMA_API_KEY')
                use_api = api_key is not None

                # Initialize the model
                self.gemma_model = GemmaModel(use_api=use_api, api_key=api_key)

                # Set database schema and metrics information
                db_schema = self.db.get_table_info()
                self.gemma_model.set_db_schema(db_schema)

                # Set metrics information from platform metrics
                metrics_info = {
                    "platforms": self.platform_metrics.categories,
                    "metrics": {}
                }

                # Add metrics for each platform
                for platform in self.platform_metrics.platform_metrics:
                    metrics_info["metrics"][platform] = self.platform_metrics.get_metrics_for_platform(platform)

                # Add podcast types
                metrics_info["podcast_types"] = self.platform_metrics.get_podcast_types()

                # Set metrics information
                self.gemma_model.set_metrics_info(metrics_info)

                # Initialize multi-agent system with the Gemma model
                self.multi_agent_system = MultiAgentSystem(self.gemma_model)
                print("Gemma model and multi-agent system initialized successfully")
            except Exception as e:
                print(f"Error initializing Gemma model: {e}")
                self.use_gemma = False

        # Initialize ADK integration if enabled
        self.adk_integration = None
        if use_adk:
            try:
                self.adk_integration = ADKIntegration()
                print("ADK integration initialized successfully")
            except Exception as e:
                print(f"Error initializing ADK integration: {e}")
                self.use_adk = False

    def process_query(self, query):
        """Process a natural language query and return results."""

        # Use Gemma for enhanced query understanding if available
        gemma_enhanced = {}
        if self.use_gemma and self.gemma_model:
            try:
                gemma_enhanced = self.gemma_model.enhance_query_understanding(query)
                print(f"Gemma enhanced understanding: {gemma_enhanced}")
            except Exception as e:
                print(f"Error using Gemma for query enhancement: {e}")

        # Use ADK to detect metric type if available
        metric_type = self.metric_calculator.get_default_metric_type()  # Default metric type

        # First try using ADK for metric type detection
        if self.use_adk and self.adk_integration:
            try:
                adk_metric_type = self.adk_integration.detect_metric_type(query)
                if adk_metric_type in self.metric_calculator.get_metric_types():
                    metric_type = adk_metric_type
                print(f"ADK detected metric type: {metric_type}")
            except Exception as e:
                print(f"Error detecting metric type with ADK: {e}")

        # Fall back to our metric calculator if ADK fails or returns an invalid type
        if metric_type not in self.metric_calculator.get_metric_types():
            metric_type = self.metric_calculator.detect_metric_type_from_query(query)
            print(f"Metric calculator detected metric type: {metric_type}")

        # Fall back to traditional classification if Gemma fails or is disabled
        classification = self._classify_query(query)
        query_type = classification["type"]
        platform = classification["platform"]
        metrics = classification["metrics"]

        # Store Gemma's enhanced understanding in the classification
        classification["gemma_enhanced"] = gemma_enhanced

        # Store ADK's detected metric type in the classification
        classification["metric_type"] = metric_type

        # Handle unique visitor queries (prioritize this over other query types)
        if metrics and "unique_visitors" in metrics:
            # Default to URL platform for unique visitor queries if no platform specified
            if platform == "social_media":
                platform = "url"
                classification["platform"] = "url"
            return self._process_url_unique_views_query(query, classification)

        # Handle other query types
        if query_type == "top":
            return self._process_top_query(query)
        elif query_type == "comparison":
            return self._process_comparison_query(query)
        elif query_type == "trend":
            return self._process_trend_query(query)
        else:
            return self._process_general_query(query, classification)

    def _classify_query(self, query):
        """Classify the type of query, detect platform, metrics, and time period."""
        query = query.lower()

        # Detect platform
        platform = self._detect_platform(query)

        # Detect time period
        time_period = self._detect_time_period(query)

        # Detect metrics based on platform
        metrics = self._detect_metrics(query, platform)

        # Classify query type
        year_match = re.search(r'\b(20\d{2})\b', query)

        # Prioritize Trend classification
        query_type = "general"
        if any(term in query for term in ["trend", "over time", "history", "change", "growth", "monthly", "yearly", "annual"]) or year_match:
             # Further check if it's a specific trend query pattern
             if any(term in query for term in ["trend", "over time", "history", "change", "growth"]) or \
                (year_match and any(term in query for term in ["monthly", "yearly", "annual", "avg", "average"])):
                 query_type = "trend"

        # Then check for Top queries
        elif any(term in query for term in ["top", "best", "highest", "most", "leading"]):
            query_type = "top"

        # Then check for Comparison queries
        elif any(term in query for term in ["compare", "versus", "vs", "difference", "between"]):
            query_type = "comparison"

        return {
            "type": query_type,
            "platform": platform,
            "time_period": time_period,
            "metrics": metrics
        }

    def _process_top_query(self, query):
        """Process a query asking for top items."""
        # Simple implementation - extract number and metric
        match = re.search(r"top\s+(\d+)", query.lower())
        limit = 5  # Default
        if match:
            limit = int(match.group(1))

        # Detect metric type from query
        metric_type = self.metric_calculator.detect_metric_type_from_query(query)

        # Determine platform category and appropriate metrics
        platform_category = "social"  # Default
        platform = None

        # Check for podcast platforms
        for podcast_platform in self.platform_metrics.categories["podcast"]:
            if podcast_platform.lower() in query.lower() or "podcast" in query.lower():
                platform_category = "podcast"
                platform = podcast_platform
                break

        # Check for social media platforms
        for social_platform in self.platform_metrics.categories["social"]:
            if social_platform.lower() in query.lower():
                platform_category = "social"
                platform = social_platform
                break

        # Check for URL/web platforms
        if "url" in query.lower():
            platform_category = "web"
            platform = "URL"
        elif "website" in query.lower():
            platform_category = "web"
            platform = "Website"

        # Get appropriate metrics based on platform or category
        if platform:
            metrics = self.platform_metrics.get_metrics_for_platform(platform)
        else:
            metrics = self.platform_metrics.get_metrics_for_category(platform_category)

        # Determine the primary metric to use
        metric = metrics["primary"][0] if metrics["primary"] else "plays"  # Default to plays

        # Check for specific metrics in the query
        for m in metrics["primary"] + metrics["secondary"]:
            if m.lower() in query.lower():
                metric = m
                break

        # Check for podcast types
        podcast_type = None

        # Check for exact podcast type matches
        for pt in self.platform_metrics.get_podcast_types():
            if pt.lower() in query.lower():
                podcast_type = pt
                break

        # Check for category keywords
        if not podcast_type:
            if "gen z" in query.lower() or "genz" in query.lower():
                podcast_type = "Gen Z"
            elif "green deal" in query.lower() or "green" in query.lower():
                podcast_type = "Green Deal"
            elif "europe" in query.lower() or "europa" in query.lower():
                podcast_type = "EUROPE"
            elif "eu und du" in query.lower() or "eu und" in query.lower():
                podcast_type = "EU und Du"
            elif "can eu" in query.lower() or "can the eu" in query.lower():
                podcast_type = "Can EU do this"
            elif "voices" in query.lower() or "brussels" in query.lower():
                podcast_type = "Voices from Brussels"

        # Determine the table based on platform category
        if platform_category == "podcast":
            # Add podcast type filter if specified
            podcast_type_filter = f"AND podcast_entries.podcast_type = '{podcast_type}'" if podcast_type else ""

            # Get SQL aggregation function and column prefix based on metric type
            agg_func = self.metric_calculator.get_sql_aggregation(metric_type)
            col_prefix = self.metric_calculator.get_column_prefix(metric_type)

            sql = f"""
            SELECT stations.name as station, countries.name as country,
                   {agg_func}(podcast_entries.{metric}) as {col_prefix}{metric},
                   podcast_entries.podcast_type
            FROM podcast_entries
            JOIN stations ON podcast_entries.station_id = stations.id
            JOIN countries ON podcast_entries.country_id = countries.id
            {podcast_type_filter}
            GROUP BY stations.name, countries.name, podcast_entries.podcast_type
            ORDER BY {col_prefix}{metric} DESC
            LIMIT {limit}
            """
        else:  # social or web

            # Get SQL aggregation function and column prefix based on metric type
            agg_func = self.metric_calculator.get_sql_aggregation(metric_type)
            col_prefix = self.metric_calculator.get_column_prefix(metric_type)

            sql = f"""
            SELECT stations.name as station, countries.name as country,
                   platforms.name as platform, {agg_func}(social_media_entries.{metric}) as {col_prefix}{metric}
            FROM social_media_entries
            JOIN stations ON social_media_entries.station_id = stations.id
            JOIN countries ON social_media_entries.country_id = countries.id
            JOIN platforms ON social_media_entries.platform_id = platforms.id
            GROUP BY stations.name, countries.name, platforms.name
            ORDER BY {col_prefix}{metric} DESC
            LIMIT {limit}
            """

        try:
            results = self.db.execute_query(sql)
            # Format numeric columns for better readability
            results = self._format_numeric_columns(results)
        except Exception as e:
            # Handle any database errors
            results = pd.DataFrame()
            print(f"Error executing query: {e}")

        # Create visualization config
        viz_config = {
            "type": "bar",
            "x": "station",
            "y": f"avg_{metric}",
            "title": f"Top {limit} Stations by {metric.capitalize()}"
        }

        insights = self._generate_insights(results, viz_config, query=query)

        return {
            "query": query,
            "results": results,
            "sql": sql,
            "method": "rule_based_extraction",
            "visualization": viz_config,
            "insights": insights,
            "selected_metric_type": "AVG"  # Default to average
        }

    def _process_comparison_query(self, query):
        """Process a query comparing items."""
        query = query.lower()

        # Check if comparing platforms
        platform_keywords = {
            "facebook": "Facebook",
            "instagram": "Instagram",
            "twitter": "Twitter",
            "x": "X",
            "youtube": "YouTube",
            "spotify": "Spotify",
            "apple": "Apple Podcast"
        }

        platforms_to_compare = []
        for keyword, platform in platform_keywords.items():
            if keyword in query:
                platforms_to_compare.append(platform)

        # If comparing platforms
        if len(platforms_to_compare) >= 2:
            metric = "reach"
            if "like" in query:
                metric = "likes"
            elif "comment" in query:
                metric = "comments"
            elif "share" in query:
                metric = "shares"

            platforms_str = "', '".join(platforms_to_compare)

            sql = f"""
            SELECT platforms.name as platform, AVG(social_media_entries.{metric}) as avg_{metric}
            FROM social_media_entries
            JOIN platforms ON social_media_entries.platform_id = platforms.id
            WHERE platforms.name IN ('{platforms_str}')
            GROUP BY platforms.name
            ORDER BY avg_{metric} DESC
            """

            results = self.db.execute_query(sql)

            # Format numeric columns for better readability
            results = self._format_numeric_columns(results)

            viz_config = {
                "type": "bar",
                "x": "platform",
                "y": f"avg_{metric}",
                "title": f"Comparison of {metric.capitalize()} Across Platforms"
            }

            insights = self._generate_insights(results, viz_config, query=query)

            return {
                "query": query,
                "results": results,
                "sql": sql,
                "method": "platform_comparison",
                "visualization": viz_config,
                "insights": insights
            }

        # Check if comparing countries
        country_pattern = r'\b([A-Z][a-z]+)\b'
        countries = re.findall(country_pattern, query)

        if len(countries) >= 2:
            metric = "reach" if "social" in query else "plays"
            if "like" in query:
                metric = "likes"

            countries_str = "', '".join(countries)

            if "podcast" in query:
                sql = f"""
                SELECT countries.name as country, AVG(podcast_entries.{metric}) as avg_{metric}
                FROM podcast_entries
                JOIN countries ON podcast_entries.country_id = countries.id
                WHERE countries.name IN ('{countries_str}')
                GROUP BY countries.name
                ORDER BY avg_{metric} DESC
                """
            else:
                sql = f"""
                SELECT countries.name as country, AVG(social_media_entries.{metric}) as avg_{metric}
                FROM social_media_entries
                JOIN countries ON social_media_entries.country_id = countries.id
                WHERE countries.name IN ('{countries_str}')
                GROUP BY countries.name
                ORDER BY avg_{metric} DESC
                """

            results = self.db.execute_query(sql)

            # Format numeric columns for better readability
            results = self._format_numeric_columns(results)

            viz_config = {
                "type": "bar",
                "x": "country",
                "y": f"avg_{metric}",
                "title": f"Comparison of {metric.capitalize()} Across Countries"
            }

            insights = self._generate_insights(results, viz_config, query=query)

            return {
                "query": query,
                "results": results,
                "sql": sql,
                "method": "country_comparison",
                "visualization": viz_config,
                "insights": insights,
                "selected_metric_type": "AVG"  # Default to average
            }

        # Default comparison (podcasts vs social media)
        return self._process_general_query(query)

    def _process_trend_query(self, query):
        """Process a query asking for trends over time."""
        query = query.lower()

        # Extract year if present
        year_match = re.search(r'\b(20\d{2})\b', query)
        year_filter = ""
        title_suffix = "Over Time"
        if year_match:
            year = year_match.group(1)
            year_filter = f"WHERE strftime('%Y', date) = '{year}'"
            title_suffix = f"for {year}"

        # Detect platform and metrics
        platform = self._detect_platform(query)
        detected_metrics = self._detect_metrics(query, platform)

        # Get human-readable platform name
        platform_display_name = self._get_platform_display_name(platform)

        # Determine if we're looking at podcasts or social media and select appropriate metric
        if platform == "podcast":
            table = "podcast_entries"
            date_col = f"{table}.date"

            # Use detected metrics if available, otherwise use default
            if detected_metrics:
                metric = detected_metrics[0]
            else:
                # Check available metrics for podcasts
                podcast_cols = self.table_info.get(table, [])
                if 'downloads' in podcast_cols:
                    metric = 'downloads'
                elif 'listeners' in podcast_cols:
                    metric = 'listeners'
                elif 'plays' in podcast_cols:
                    metric = 'plays' # Fallback to plays
                else:
                    metric = 'plays'
        elif platform == "url":
            table = "social_media_entries"
            date_col = f"{table}.date"

            # For URL queries, prioritize visits or unique_visitors
            if detected_metrics:
                metric = detected_metrics[0]
            else:
                if "visit" in query or "visits" in query:
                    metric = "visits"
                elif "unique" in query or "visitor" in query or "visitors" in query:
                    metric = "unique_visitors"
                else:
                    metric = "page_views"

            # Add platform filter for URL
            platform_join = "JOIN platforms ON social_media_entries.platform_id = platforms.id"
            if year_filter:
                year_filter += " AND platforms.name = 'URL'"
            else:
                year_filter = "WHERE platforms.name = 'URL'"
        else:
            # For social media platforms
            table = "social_media_entries"
            date_col = f"{table}.date"

            # Use detected metrics if available, otherwise use default
            if detected_metrics:
                metric = detected_metrics[0]
            else:
                # Default social media metric
                metric = "reach"
                if "like" in query: metric = "likes"
                elif "comment" in query: metric = "comments"
                elif "share" in query: metric = "shares"
                elif "engagement" in query: metric = "engagement"

            # Add platform filter for specific social media platforms
            if platform not in ["social_media", "podcast", "url"]:
                if year_filter:
                    year_filter += f" AND platforms.name = '{platform.capitalize()}'"
                else:
                    year_filter = f"WHERE platforms.name = '{platform.capitalize()}'"

        # Get human-readable metric name
        metric_display_name = self._get_metric_display_name(metric)

        metric_col = f"{table}.{metric}"

        # Add join for platforms if needed
        platform_join = ""
        if platform != "podcast" and ("platforms.name" in year_filter or platform == "url"):
            platform_join = "JOIN platforms ON social_media_entries.platform_id = platforms.id"

        try:
            sql = f"""
            SELECT
                strftime('%Y-%m', {date_col}) as month,
                AVG({metric_col}) as avg_{metric}
            FROM {table}
            {platform_join}
            {year_filter}
            GROUP BY month
            ORDER BY month
            """
        except Exception as e:
            print(f"Error generating SQL: {e}")
            # Provide a fallback SQL for trend queries
            sql = f"""
            SELECT
                strftime('%Y-%m', date) as month,
                AVG(reach) as avg_reach
            FROM social_media_entries
            GROUP BY month
            ORDER BY month
            """

        results = self.db.execute_query(sql)

        # Format numeric columns for better readability
        results = self._format_numeric_columns(results)

        # Create title with platform information
        title = f"Monthly Average {metric_display_name} {title_suffix}"
        if platform != "social_media":
            title += f" on {platform_display_name}"

        viz_config = {
            "type": "line",
            "x": "month",
            "y": f"avg_{metric}",
            "title": title,
            # Add margin to bottom of chart to prevent x-axis label overlap
            "layout": {
                "margin": {"b": 80},  # Increase bottom margin
                "xaxis": {
                    "tickangle": -45,  # Angle the x-axis labels
                    "automargin": True  # Automatically adjust margins
                }
            }
        }

        insights = self._generate_insights(results, viz_config, platform_display_name, metric_display_name, query=query)

        # Use the detected metric type if available
        metric_type = "AVG"  # Default to average

        # Check if we have ADK integration and it's enabled
        if hasattr(self, 'use_adk') and self.use_adk and hasattr(self, 'adk_integration') and self.adk_integration:
            try:
                # Detect the metric type from the query
                metric_type = self.adk_integration.detect_metric_type(query)
            except Exception as e:
                print(f"Error detecting metric type for trend query: {e}")

        return {
            "query": query,
            "results": results,
            "sql": sql,
            "method": "trend_analysis",
            "visualization": viz_config,
            "insights": insights,
            "selected_metric_type": metric_type
        }

    def _process_url_unique_views_query(self, query, classification):
        """Process a query specifically about URL unique views."""
        time_period = classification["time_period"]

        # Default to last 3 months if no time period specified
        if not time_period:
            time_period = {"unit": "month", "value": 3}
            classification["time_period"] = time_period

        time_filter = self._build_time_filter(time_period)

        # Get human-readable platform and metric names
        platform_display_name = self._get_platform_display_name("url")
        metric_display_name = self._get_metric_display_name("unique_visitors")

        # Build SQL for URL unique views by station
        sql = f"""
        SELECT
            s.name as station,
            SUM(sme.unique_visitors) as total_unique_views,
            COUNT(sme.link) as url_count
        FROM social_media_entries sme
        JOIN stations s ON sme.station_id = s.id
        JOIN platforms p ON sme.platform_id = p.id
        WHERE p.name = 'URL' AND sme.unique_visitors IS NOT NULL
        {time_filter}
        GROUP BY s.name
        ORDER BY total_unique_views DESC
        """

        results = self.db.execute_query(sql)

        # Format numeric columns for better readability
        results = self._format_numeric_columns(results)

        # Create time period text for title
        time_text = ""
        if time_period:
            if time_period["unit"] == "month" and time_period["value"]:
                time_text = f" for Last {time_period['value']} Months"
            elif time_period["unit"] == "year" and time_period["value"]:
                time_text = f" for Last {time_period['value']} Years"
            elif time_period["unit"] == "day" and time_period["value"]:
                time_text = f" for Last {time_period['value']} Days"
            elif time_period["unit"] == "week" and time_period["value"]:
                time_text = f" for Last {time_period['value']} Weeks"

        # Create visualization config with improved title
        viz_config = {
            "type": "bar",
            "x": "station",
            "y": "total_unique_views",
            "title": f"Station-wise {metric_display_name} on {platform_display_name}{time_text}",
            # Add margin to bottom of chart to prevent x-axis label overlap
            "layout": {
                "margin": {"b": 120},  # Increase bottom margin
                "xaxis": {
                    "tickangle": -45,  # Angle the x-axis labels
                    "automargin": True  # Automatically adjust margins
                }
            }
        }

        # Generate insights
        insights = []
        if not results.empty:
            top_station = results.iloc[0]
            insights.append(f"{top_station['station']} has the highest {metric_display_name.lower()} on {platform_display_name} with {int(top_station['total_unique_views']):,} views across {int(top_station['url_count'])} URLs.")

            if len(results) > 1:
                second_station = results.iloc[1]
                insights.append(f"{second_station['station']} is second with {int(second_station['total_unique_views']):,} views across {int(second_station['url_count'])} URLs.")

            total_views = results['total_unique_views'].sum()
            total_urls = results['url_count'].sum()
            insights.append(f"Total {metric_display_name.lower()} across all stations: {int(total_views):,} from {int(total_urls)} URLs.")

            # Add time period context to insights
            if time_period:
                if time_period["unit"] == "month" and time_period["value"]:
                    insights.append(f"This data covers the last {time_period['value']} months.")
                elif time_period["unit"] == "year" and time_period["value"]:
                    insights.append(f"This data covers the last {time_period['value']} years.")
        else:
            insights = [f"No data available for {metric_display_name.lower()} on {platform_display_name} in the specified time period."]

        # Format the results for better readability
        if not results.empty:
            # Format numbers with commas for thousands
            results['total_unique_views'] = results['total_unique_views'].apply(lambda x: f"{int(x):,}")

        # Return results
        return {
            "query": query,
            "results": results,
            "sql": sql,
            "method": "url_unique_views_analysis",
            "visualization": viz_config,
            "insights": insights,
            "selected_metric_type": classification.get("metric_type", "AVG")
        }

    def _process_general_query(self, query, classification=None):
        """Process a general query."""
        query = query.lower()

        # Use classification if provided, otherwise use simple detection
        if classification:
            platform = classification["platform"]
            time_period = classification["time_period"]
            metrics = classification["metrics"]

            # Default metrics if none detected
            if not metrics:
                if platform == "podcast":
                    metrics = ["plays"]
                elif platform == "url":
                    metrics = ["page_views"]
                else:
                    metrics = ["reach"]

            # Use first metric for query
            metric = metrics[0]
            time_filter = self._build_time_filter(time_period)
        else:
            # Legacy fallback
            platform = "podcast" if "podcast" in query else "social_media"
            metric = "reach" if "social" in query else "plays"
            if "like" in query:
                metric = "likes"
            elif "comment" in query:
                metric = "comments"
            elif "share" in query:
                metric = "shares"
            time_filter = ""

        # Get human-readable platform name
        platform_display_name = self._get_platform_display_name(platform)

        # Get human-readable metric name
        metric_display_name = self._get_metric_display_name(metric)

        if platform == "podcast":
            sql = f"""
            SELECT
                stations.name as station,
                countries.name as country,
                AVG(podcast_entries.{metric}) as avg_{metric}
            FROM podcast_entries
            JOIN stations ON podcast_entries.station_id = stations.id
            JOIN countries ON podcast_entries.country_id = countries.id
            {time_filter}
            GROUP BY stations.name, countries.name
            ORDER BY avg_{metric} DESC
            LIMIT 10
            """
        elif platform == "url":
            # Special case for URL platform
            sql = f"""
            SELECT
                stations.name as station,
                countries.name as country,
                platforms.name as platform,
                AVG(social_media_entries.{metric}) as avg_{metric}
            FROM social_media_entries
            JOIN stations ON social_media_entries.station_id = stations.id
            JOIN countries ON social_media_entries.country_id = countries.id
            JOIN platforms ON social_media_entries.platform_id = platforms.id
            WHERE platforms.name = 'URL'
            {time_filter}
            GROUP BY stations.name, countries.name, platforms.name
            ORDER BY avg_{metric} DESC
            LIMIT 10
            """
        else:
            # For specific social media platform or all social media
            platform_filter = ""
            if platform not in ["social_media", "podcast"]:
                platform_filter = f"AND platforms.name = '{platform.capitalize()}'"

            sql = f"""
            SELECT
                stations.name as station,
                countries.name as country,
                platforms.name as platform,
                AVG(social_media_entries.{metric}) as avg_{metric}
            FROM social_media_entries
            JOIN stations ON social_media_entries.station_id = stations.id
            JOIN countries ON social_media_entries.country_id = countries.id
            JOIN platforms ON social_media_entries.platform_id = platforms.id
            WHERE 1=1 {platform_filter}
            {time_filter}
            GROUP BY stations.name, countries.name, platforms.name
            ORDER BY avg_{metric} DESC
            LIMIT 10
            """

        results = self.db.execute_query(sql)

        # Format numeric columns for better readability
        results = self._format_numeric_columns(results)

        # Apply ADK metric processing if available
        if self.use_adk and self.adk_integration:
            try:
                # Get the metric type from classification
                metric_type = classification.get("metric_type", "AVG")

                # Process the results with ADK
                results = self.adk_integration.process_metrics(results, metric_type)

                # Update the metric name based on the metric type
                if metric_type != "AVG" and any(col.startswith(metric_type.lower() + "_") for col in results.columns):
                    metric = next((col.split("_", 1)[1] for col in results.columns
                                  if col.startswith(metric_type.lower() + "_")), metric)
                    metric_prefix = metric_type.lower()
                else:
                    metric_prefix = "avg"
            except Exception as e:
                print(f"Error processing metrics with ADK: {e}")
                metric_prefix = "avg"  # Fall back to average on error
        else:
            metric_prefix = "avg"  # Default to average if ADK is not available

        x_column = "station"
        if "country" in query:
            x_column = "country"
        elif "platform" in query and platform == "social_media":
            x_column = "platform"

        # Create a title that includes the platform information
        title = f"Overview of {metric_display_name} by {x_column.capitalize()}"
        if platform != "social_media":
            title += f" on {platform_display_name}"

        # Use the appropriate metric prefix in the visualization config
        viz_config = {
            "type": "bar",
            "x": x_column,
            "y": f"{metric_prefix}_{metric}",
            "title": title
        }

        insights = self._generate_insights(results, viz_config, platform_display_name, metric_display_name, query=query)

        return {
            "query": query,
            "results": results,
            "sql": sql,
            "method": "general_query",
            "visualization": viz_config,
            "insights": insights,
            "selected_metric_type": classification.get("metric_type", "AVG")  # Include the detected metric type
        }

    def _get_platform_display_name(self, platform):
        """Get a human-readable platform name."""
        platform_names = {
            "url": "Websites",
            "facebook": "Facebook",
            "twitter": "Twitter",
            "instagram": "Instagram",
            "youtube": "YouTube",
            "podcast": "Podcasts",
            "social_media": "Social Media"
        }
        return platform_names.get(platform, platform.capitalize())

    def _get_metric_display_name(self, metric):
        """Get a human-readable metric name."""
        metric_names = {
            "page_views": "Page Views",
            "unique_visitors": "Unique Visitors",
            "visits": "Visits",
            "reach": "Reach",
            "impressions": "Impressions",
            "likes": "Likes",
            "comments": "Comments",
            "shares": "Shares",
            "engagements": "Engagements",
            "retweets": "Retweets",
            "link_clicks": "Link Clicks",
            "video_views": "Video Views",
            "watch_time": "Watch Time",
            "subscribers": "Subscribers",
            "plays": "Plays",
            "downloads": "Downloads"
        }
        return metric_names.get(metric, metric.replace("_", " ").capitalize())

    def _detect_platform(self, query):
        """Detect which platform the query is about."""
        query = query.lower()

        # Platform detection keywords
        platform_keywords = {
            "url": ["url", "website", "web", "site", "page", "link", "webpage"],
            "facebook": ["facebook", "fb", "meta"],
            "twitter": ["twitter", "tweet", "x"],
            "instagram": ["instagram", "insta", "ig"],
            "youtube": ["youtube", "yt", "video"],
            "podcast": ["podcast", "spotify", "apple", "sound", "audio"]
        }

        # Check for platform keywords
        for platform, keywords in platform_keywords.items():
            if any(keyword in query for keyword in keywords):
                return platform

        # Default to social media if no specific platform is detected
        return "social_media"

    def _detect_time_period(self, query):
        """Detect time period mentioned in the query."""
        query = query.lower()

        # Check for specific time periods
        month_match = re.search(r'(?:last|past|previous)\s+(\d+)\s+months?', query)
        if month_match:
            return {"unit": "month", "value": int(month_match.group(1))}

        year_match = re.search(r'(?:last|past|previous)\s+(\d+)\s+years?', query)
        if year_match:
            return {"unit": "year", "value": int(year_match.group(1))}

        # Check for specific years
        specific_year = re.search(r'\b(20\d{2})\b', query)
        if specific_year:
            return {"unit": "specific_year", "value": specific_year.group(1)}

        # Check for other time periods
        if "today" in query or "daily" in query:
            return {"unit": "day", "value": 1}
        elif "this week" in query or "weekly" in query:
            return {"unit": "week", "value": 1}
        elif "this month" in query or "monthly" in query:
            return {"unit": "month", "value": 1}
        elif "this year" in query or "yearly" in query or "annual" in query:
            return {"unit": "year", "value": 1}

        # Default to no time period
        return None

    def _detect_metrics(self, query, platform):
        """Detect metrics mentioned in the query based on platform."""
        query = query.lower()

        # Platform-specific metric mappings with more synonyms and variations
        platform_metrics = {
            "url": {
                "view": "page_views",
                "page view": "page_views",
                "pageview": "page_views",
                "unique view": "unique_visitors",
                "unique visitor": "unique_visitors",
                "unique visitors": "unique_visitors",
                "visitor": "unique_visitors",
                "visitors": "unique_visitors",
                "visit": "visits",
                "visits": "visits",
                "traffic": "visits"
            },
            "facebook": {
                "reach": "reach",
                "impression": "impressions",
                "view": "impressions",
                "like": "likes",
                "comment": "comments",
                "share": "shares",
                "engagement": "engagements"
            },
            "twitter": {
                "impression": "impressions",
                "view": "impressions",
                "engagement": "engagements",
                "interact": "engagements",
                "retweet": "retweets",
                "click": "link_clicks",
                "link click": "link_clicks"
            },
            "instagram": {
                "reach": "reach",
                "impression": "impressions",
                "view": "impressions",
                "like": "likes",
                "comment": "comments",
                "engagement": "engagements"
            },
            "youtube": {
                "view": "video_views",
                "video view": "video_views",
                "watch": "video_views",
                "watch time": "watch_time",
                "duration": "watch_time",
                "subscriber": "subscribers",
                "sub": "subscribers"
            },
            "podcast": {
                "play": "plays",
                "listen": "plays",
                "stream": "plays",
                "subscriber": "subscribers",
                "follower": "subscribers",
                "download": "downloads"
            },
            "social_media": {
                "reach": "reach",
                "impression": "impressions",
                "view": "impressions",
                "like": "likes",
                "comment": "comments",
                "share": "shares",
                "engagement": "engagements",
                "interact": "engagements"
            }
        }

        # Get metrics for the detected platform
        metrics_map = platform_metrics.get(platform, platform_metrics["social_media"])

        # Check for metrics in the query
        detected_metrics = []
        for keyword, metric in metrics_map.items():
            if keyword in query:
                detected_metrics.append(metric)

        # Return unique metrics
        return list(set(detected_metrics)) if detected_metrics else None

    def _build_time_filter(self, time_period):
        """Build SQL time filter based on detected time period."""
        if not time_period:
            return ""

        if time_period["unit"] == "month" and time_period["value"]:
            return f"AND date >= date('now', '-{time_period['value']} months')"
        elif time_period["unit"] == "year" and time_period["value"]:
            return f"AND date >= date('now', '-{time_period['value']} years')"
        elif time_period["unit"] == "specific_year" and time_period["value"]:
            return f"AND strftime('%Y', date) = '{time_period['value']}'"
        elif time_period["unit"] == "day" and time_period["value"]:
            return f"AND date >= date('now', '-{time_period['value']} days')"
        elif time_period["unit"] == "week" and time_period["value"]:
            return f"AND date >= date('now', '-{time_period['value']} * 7 days')"

        return ""

    def _generate_insights(self, results, viz_config, platform_name=None, metric_name=None, query=None):
        """Generate insights from the results."""

        # Check if we can use the multi-agent system for insights
        if self.multi_agent_system and query:
            try:
                # Create context for the agents
                context = {
                    "platform": platform_name,
                    "metric": metric_name,
                    "viz_config": viz_config
                }

                # Use multi-agent system to generate insights
                agent_insights = self.multi_agent_system.generate_insights(query, results, context)
                if agent_insights and len(agent_insights) > 0:
                    return agent_insights
            except Exception as e:
                print(f"Error generating insights with multi-agent system: {e}")
                # Fall back to Gemma or rule-based insights

        # Fall back to Gemma for insights if multi-agent system fails
        if self.use_gemma and self.gemma_model and query:
            try:
                # Use Gemma to generate insights
                gemma_insights = self.gemma_model.generate_insights(query, results)
                if gemma_insights and len(gemma_insights) > 0:
                    return gemma_insights
            except Exception as e:
                print(f"Error generating insights with Gemma: {e}")
                # Fall back to rule-based insights

        # Traditional rule-based insights generation
        insights = []

        if results.empty:
            return ["No data available to analyze."]

        y_col = viz_config.get("y")
        x_col = viz_config.get("x")

        if not y_col or not x_col or y_col not in results.columns or x_col not in results.columns:
             return ["Could not generate insights due to missing data columns."]

        # Use provided metric name or extract from y_col
        if not metric_name:
            # Extract base metric name using our metric calculator
            for prefix in self.metric_calculator.metric_type_prefixes.values():
                if y_col.startswith(prefix):
                    metric_name = y_col[len(prefix):]
                    break
            else:
                metric_name = y_col

            # Format the metric name for display
            metric_name = metric_name.replace('_', ' ').title()

        # Use provided platform name or default to empty
        platform_context = f" on {platform_name}" if platform_name and platform_name != "Social Media" else ""

        # Extract key insights based on the visualization type and data
        if viz_config["type"] == "bar":

            # Get the top performer
            top_row = results.iloc[0]
            insights.append(f"{top_row[x_col]} has the highest {metric_name}{platform_context} at {float(top_row[y_col]):.2f}")

            # Compare top to average
            avg_val = results[y_col].mean()
            insights.append(f"The average {y_col.replace('avg_', '').replace('_', ' ')} is {avg_val:.2f}")

            # Highlight any outliers
            if len(results) > 1:
                std_dev = results[y_col].std()
                mean_val = results[y_col].mean()
                outliers = results[results[y_col] > mean_val + 1.5*std_dev]
                if not outliers.empty:
                    for _, row in outliers.iterrows():
                        insights.append(f"{row[x_col]} is significantly above average with {float(row[y_col]):.2f}")

            # Compare top to bottom if multiple results
            if len(results) > 1:
                bottom_row = results.iloc[-1]
                # Avoid division by zero or near-zero
                if abs(bottom_row[y_col]) > 1e-6:
                    diff_pct = ((top_row[y_col] - bottom_row[y_col]) / bottom_row[y_col]) * 100
                    insights.append(f"{top_row[x_col]} has {diff_pct:.1f}% higher {y_col.replace('avg_', '').replace('_', ' ')} than {bottom_row[x_col]}")
                else:
                    insights.append(f"{top_row[x_col]} is significantly higher than {bottom_row[x_col]} (which is near zero).")

        elif viz_config["type"] == "line":
            # Check for trends
            if len(results) >= 2: # Need at least 2 points for a trend
                # Ensure data is sorted by month for correct trend calculation
                results_sorted = results.sort_values(by=x_col)
                first_val = results_sorted.iloc[0][y_col]
                last_val = results_sorted.iloc[-1][y_col]
                first_month = results_sorted.iloc[0][x_col]
                last_month = results_sorted.iloc[-1][x_col]

                # Avoid division by zero for percentage calculation
                if abs(first_val) > 1e-6:
                    diff_pct = ((last_val - first_val) / first_val) * 100
                    trend_direction = "increased" if diff_pct > 0 else "decreased"
                    insights.append(f"Overall, {y_col.replace('avg_', '').replace('_', ' ').capitalize()} {trend_direction} by {abs(diff_pct):.1f}% from {first_month} to {last_month}.")
                elif last_val > first_val:
                     insights.append(f"Overall, {y_col.replace('avg_', '').replace('_', ' ').capitalize()} increased from near zero to {last_val:.2f} between {first_month} and {last_month}.")
                elif last_val < first_val:
                     insights.append(f"Overall, {y_col.replace('avg_', '').replace('_', ' ').capitalize()} decreased from {first_val:.2f} to {last_val:.2f} between {first_month} and {last_month}.")
                else:
                     insights.append(f"Overall, {y_col.replace('avg_', '').replace('_', ' ').capitalize()} remained stable around {first_val:.2f} between {first_month} and {last_month}.")

                # Find peak month
                peak_idx = results_sorted[y_col].idxmax()
                peak_row = results_sorted.iloc[peak_idx]
                insights.append(f"The peak month was {peak_row[x_col]} with an average of {float(peak_row[y_col]):.2f} {y_col.replace('avg_', '').replace('_', ' ')}.")

                # Find lowest month
                low_idx = results_sorted[y_col].idxmin()
                low_row = results_sorted.iloc[low_idx]
                insights.append(f"The lowest month was {low_row[x_col]} with an average of {float(low_row[y_col]):.2f} {y_col.replace('avg_', '').replace('_', ' ')}.")

        return insights

    def _format_numeric_columns(self, df):
        """Format numeric columns in a DataFrame for better readability."""
        if df.empty:
            return df

        # Create a copy to avoid modifying the original
        formatted_df = df.copy()

        # Format numeric columns
        for col in formatted_df.columns:
            if col.startswith('avg_') or col.startswith('total_') or formatted_df[col].dtype.kind in 'ifc':
                try:
                    # Round to 1 decimal place if it's not a whole number
                    formatted_df[col] = formatted_df[col].apply(lambda x: int(x) if x == int(x) else round(x, 1))
                except:
                    # Skip if column can't be formatted
                    pass

        return formatted_df
