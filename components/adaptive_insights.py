"""
Adaptive Insights component for Euranet Dashboard.
This component creates adaptive layouts for insights based on their complexity and relationships.
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
from components.insight_analyzer import InsightAnalyzer

class AdaptiveInsights:
    """Creates adaptive layouts for insights based on their complexity and relationships."""

    def __init__(self, dark_mode=True):
        """
        Initialize the adaptive insights component.
        
        Args:
            dark_mode: Whether to use dark mode styling.
        """
        self.dark_mode = dark_mode
        self.analyzer = InsightAnalyzer()
        
        # Define colors based on theme
        self.colors = {
            "primary": "#6200ee" if dark_mode else "#3700b3",
            "secondary": "#03dac6" if dark_mode else "#018786",
            "background": "#121212" if dark_mode else "#ffffff",
            "surface": "#1e1e1e" if dark_mode else "#f5f5f5",
            "text": "#ffffff" if dark_mode else "#000000",
            "text_secondary": "#b0b0b0" if dark_mode else "#666666",
            "border": "#333333" if dark_mode else "#e0e0e0",
            "comparison": "#bb86fc" if dark_mode else "#6200ee",
            "trend": "#03dac6" if dark_mode else "#018786",
            "statistical": "#cf6679" if dark_mode else "#b00020",
            "general": "#6200ee" if dark_mode else "#3700b3"
        }

    def create_adaptive_insights(self, insights, query=None, results=None):
        """
        Create adaptive layout for insights.
        
        Args:
            insights: List of insight strings
            query: Original user query (optional)
            results: Query results dataframe (optional)
            
        Returns:
            html.Div: The adaptive insights container.
        """
        if not insights:
            return html.Div("No insights available.", className="no-insights-message")
        
        # Analyze insights
        analyzed_insights = self.analyzer.analyze_insights(insights, query, results)
        
        # Group insights by category
        categorized_insights = self._group_by_category(analyzed_insights)
        
        # Create layout based on insights
        if len(analyzed_insights) <= 2:
            # Simple layout for few insights
            return self._create_simple_layout(analyzed_insights)
        elif len(categorized_insights) > 1:
            # Tabbed layout for multiple categories
            return self._create_tabbed_layout(categorized_insights)
        else:
            # Card layout for single category with multiple insights
            return self._create_card_layout(analyzed_insights)

    def _create_simple_layout(self, analyzed_insights):
        """Create a simple layout for a small number of insights."""
        insight_items = []
        
        for insight in analyzed_insights:
            # Create insight item with appropriate styling based on complexity and category
            item = html.Div(
                className=f"insight-item insight-{insight['complexity']} insight-category-{insight['category']}",
                children=[
                    html.Div(
                        className="insight-content",
                        children=[
                            html.Span(insight["text"], className="insight-text")
                        ]
                    )
                ]
            )
            insight_items.append(item)
        
        return html.Div(
            className="insights-container simple-layout",
            children=insight_items
        )

    def _create_tabbed_layout(self, categorized_insights):
        """Create a tabbed layout for insights grouped by category."""
        tabs = []
        
        # Create a tab for each category
        for category, insights in categorized_insights.items():
            # Skip categories with no insights
            if not insights:
                continue
            
            # Create insight items for this category
            category_items = []
            for insight in insights:
                item = html.Div(
                    className=f"insight-item insight-{insight['complexity']}",
                    children=[
                        html.Span(insight["text"], className="insight-text")
                    ]
                )
                category_items.append(item)
            
            # Create tab content
            tab_content = html.Div(
                className="category-insights-container",
                children=category_items
            )
            
            # Create tab
            tab = dbc.Tab(
                tab_content,
                label=category.capitalize(),
                className=f"insight-tab-{category}",
                tab_id=f"tab-{category}"
            )
            tabs.append(tab)
        
        # Create tabs container
        return dbc.Tabs(
            tabs,
            className="insights-tabs",
            active_tab=f"tab-{list(categorized_insights.keys())[0]}"
        )

    def _create_card_layout(self, analyzed_insights):
        """Create a card layout for a single category with multiple insights."""
        cards = []
        
        # Group insights by importance
        high_importance = [i for i in analyzed_insights if i["importance"] >= 8]
        medium_importance = [i for i in analyzed_insights if 5 <= i["importance"] < 8]
        low_importance = [i for i in analyzed_insights if i["importance"] < 5]
        
        # Create featured card for high importance insights
        if high_importance:
            featured_items = []
            for insight in high_importance:
                item = html.Div(
                    className=f"insight-item insight-{insight['complexity']} featured-insight",
                    children=[
                        html.Span(insight["text"], className="insight-text")
                    ]
                )
                featured_items.append(item)
            
            featured_card = html.Div(
                className="insight-card featured-card",
                children=[
                    html.Div("Key Findings", className="card-title"),
                    html.Div(
                        className="card-content",
                        children=featured_items
                    )
                ]
            )
            cards.append(featured_card)
        
        # Create card for medium importance insights
        if medium_importance:
            medium_items = []
            for insight in medium_importance:
                item = html.Div(
                    className=f"insight-item insight-{insight['complexity']}",
                    children=[
                        html.Span(insight["text"], className="insight-text")
                    ]
                )
                medium_items.append(item)
            
            medium_card = html.Div(
                className="insight-card",
                children=[
                    html.Div("Additional Insights", className="card-title"),
                    html.Div(
                        className="card-content",
                        children=medium_items
                    )
                ]
            )
            cards.append(medium_card)
        
        # Create card for low importance insights if there are any
        if low_importance:
            low_items = []
            for insight in low_importance:
                item = html.Div(
                    className=f"insight-item insight-{insight['complexity']}",
                    children=[
                        html.Span(insight["text"], className="insight-text")
                    ]
                )
                low_items.append(item)
            
            low_card = html.Div(
                className="insight-card secondary-card",
                children=[
                    html.Div("Other Observations", className="card-title"),
                    html.Div(
                        className="card-content",
                        children=low_items
                    )
                ]
            )
            cards.append(low_card)
        
        return html.Div(
            className="insights-container card-layout",
            children=cards
        )

    def _group_by_category(self, analyzed_insights):
        """Group insights by category."""
        categories = {
            "comparison": [],
            "trend": [],
            "statistical": [],
            "general": []
        }
        
        for insight in analyzed_insights:
            category = insight["category"]
            if category in categories:
                categories[category].append(insight)
        
        return {k: v for k, v in categories.items() if v}
