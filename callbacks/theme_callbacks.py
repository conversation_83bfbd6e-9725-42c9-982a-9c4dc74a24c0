"""
Theme-related callbacks for the Euranet Dashboard application.
This file contains callbacks for handling theme switching.
"""

import dash
from dash import callback, Input, Output, State

from constants import *

def register_theme_callbacks():
    """
    Register all theme-related callbacks.
    """
    @callback(
        Output(THEME_STORE_ID, "data"),
        Output(APP_CONTAINER_ID, "className"),
        Output(THEME_ICON_ID, "className"),
        Input(THEME_TOGGLE_BUTTON_ID, "n_clicks"),
        State(THEME_STORE_ID, "data"),
        prevent_initial_call=True,
    )
    def toggle_theme(n_clicks, current_theme_data):
        """
        Toggle between light and dark themes.
        
        Args:
            n_clicks: Number of times the theme toggle button has been clicked.
            current_theme_data: The current theme state data.
            
        Returns:
            tuple: Updated theme data, container class name, and icon class name.
        """
        if n_clicks is None:
            return dash.no_update, dash.no_update, dash.no_update

        is_dark = current_theme_data.get('dark_mode', True)
        new_theme_data = {'dark_mode': not is_dark}
        new_container_class = "app-container light-mode" if is_dark else "app-container dark-mode"
        new_icon_class = "fas fa-sun" if is_dark else "fas fa-moon"  # Sun for light, Moon for dark

        return new_theme_data, new_container_class, new_icon_class
