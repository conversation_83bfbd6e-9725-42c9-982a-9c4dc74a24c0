"""
Visualization-related callbacks for the Euranet Dashboard application.
This file contains callbacks for handling data visualization and insights.
"""

import pandas as pd
import dash
from dash import callback, Input, Output, State, html, dcc
import dash_bootstrap_components as dbc

from constants import *
from components.visualizer import Visualizer
from components.adaptive_insights import AdaptiveInsights
from components.metric_calculator import MetricCalculator

def register_visualization_callbacks():
    """
    Register all visualization-related callbacks.
    """
    # Initialize metric calculator
    metric_calculator = MetricCalculator()

    # Get metric options from the calculator
    metric_options = [
        {"label": metric_calculator.get_display_name(mt), "value": mt}
        for mt in metric_calculator.get_metric_types()
    ]

    @callback(
        Output(KEY_INSIGHTS_METRIC_SELECTOR_ID, "children"),
        Input(CURRENT_RESULT_STORE_ID, "data")
    )
    def update_metric_selector(result_data):
        """
        Update the metric selector dropdown based on available data.

        Args:
            result_data: The current query result data.

        Returns:
            dcc.Dropdown: The metric selector dropdown component.
        """
        if not result_data:
            # Return empty dropdown if no data
            return []

        # Get the current metric type from the result data
        current_metric_type = result_data.get("selected_metric_type", "AVG")
        print(f"Current metric type for selector: {current_metric_type}")

        # Create the dropdown component
        return dcc.Dropdown(
            id="metric-type-dropdown",
            options=metric_options,
            value=current_metric_type,  # Use the current metric type
            clearable=False,
            searchable=False,
            className="metric-dropdown"
        )

    @callback(
        Output(CURRENT_RESULT_STORE_ID, "data", allow_duplicate=True),
        Input("metric-type-dropdown", "value"),
        State(CURRENT_RESULT_STORE_ID, "data"),
        prevent_initial_call=True
    )
    def update_metric_calculation(metric_type, result_data):
        """
        Update the result data based on the selected metric type.

        Args:
            metric_type: The selected metric type (AVG, SUM, COUNT, PERCENTAGE).
            result_data: The current query result data.

        Returns:
            dict: Updated result data with the new metric calculation.
        """
        if not result_data or not metric_type:
            return dash.no_update

        # Make a copy of the result data to avoid modifying the original
        updated_data = result_data.copy()

        # Store the selected metric type in the result data
        updated_data["selected_metric_type"] = metric_type

        return updated_data
    @callback(
        Output(VISUALIZATION_CONTENT_AREA_ID, "children"),
        Output(KEY_INSIGHTS_CONTENT_ID, "children"),
        Input(CURRENT_RESULT_STORE_ID, "data"),  # Triggered when result data changes
        State(THEME_STORE_ID, "data")  # Still need theme state for Visualizer init
    )
    def update_data_panel(latest_result_data, theme_data):
        """
        Update the data visualization panel based on query results.

        Args:
            latest_result_data: The current query result data.
            theme_data: The current theme state.

        Returns:
            tuple: Visualization content and key insights content.
        """
        dark_mode = theme_data.get('dark_mode', True)  # Needed for Visualizer

        if not latest_result_data:
            # Show welcome message if no result yet
            return (
                html.Div(className="empty-state-container", children=[
                    html.Div("📊", className="empty-state-icon"),
                    html.H3("Ask a question to see insights", className="empty-state-title"),
                    html.P("Your query results and visualizations will appear here", className="empty-state-text")
                ]),
                # Empty key insights when no data
                []
            )

        # --- Render results ---
        visualizer = Visualizer(dark_mode=dark_mode)  # Create visualizer with current theme

        # Safer access to query from the stored data
        query_text = latest_result_data.get("query", "N/A")

        content = [
            html.Div(className="query-info", children=[
                html.Div("CURRENT QUERY", className="subtitle"),
                html.P(f'"{query_text}"', className="query-text")
            ])
        ]

        # Visualization (with safer access using stored data)
        viz_config = latest_result_data.get("visualization")
        # Reconstruct DataFrame from stored dict if needed for visualization
        results_list = latest_result_data.get("results")
        results_df_for_viz = pd.DataFrame(results_list) if results_list else pd.DataFrame()

        # Check if we have a selected metric type
        selected_metric_type = latest_result_data.get("selected_metric_type", "AVG")
        print(f"Selected metric type from data: {selected_metric_type}")

        # Update the visualization config based on the selected metric type
        if viz_config and "y" in viz_config:
            # Get the current y column
            current_y = viz_config["y"]
            print(f"Current y column: {current_y}")

            # If the current y column starts with a metric prefix (avg_, sum_, etc.)
            if any(current_y.startswith(prefix + "_") for prefix in ["avg", "sum", "count", "percentage"]):
                # Extract the base metric name (e.g., "plays" from "avg_plays")
                base_metric = current_y.split("_", 1)[1] if "_" in current_y else current_y
                print(f"Base metric: {base_metric}")

                # Create the new y column name based on the selected metric type
                new_y = f"{selected_metric_type.lower()}_{base_metric}"
                print(f"New y column: {new_y}")

                # Check if the column exists in the results
                if new_y in results_df_for_viz.columns:
                    # Update the visualization config
                    viz_config["y"] = new_y
                    print(f"Updated y column to: {new_y}")
                else:
                    # If the column doesn't exist, we need to process the data
                    print(f"Column {new_y} not found in results, processing data...")

                    # Apply the metric type to the results
                    if selected_metric_type == "SUM" and "avg_" in current_y:
                        # Create a sum column by multiplying avg by 10 (placeholder)
                        sum_col = current_y.replace("avg_", "sum_")
                        results_df_for_viz[sum_col] = results_df_for_viz[current_y] * 10
                        viz_config["y"] = sum_col
                    elif selected_metric_type == "COUNT" and "avg_" in current_y:
                        # Create a count column with placeholder values
                        count_col = current_y.replace("avg_", "count_")
                        results_df_for_viz[count_col] = 100
                        viz_config["y"] = count_col
                    elif selected_metric_type == "PERCENTAGE" and "avg_" in current_y:
                        # Create a percentage column
                        pct_col = current_y.replace("avg_", "percentage_")
                        total = results_df_for_viz[current_y].sum()
                        results_df_for_viz[pct_col] = (results_df_for_viz[current_y] / total) * 100
                        viz_config["y"] = pct_col

        if viz_config and not results_df_for_viz.empty:
            try:
                # Get chart title and platform information
                chart_title = viz_config.get("title", "Visualization")
                platform = viz_config.get("platform", "")
                platform_attr = {"data-platform": platform} if platform else {}

                # Create visualization with empty title (to avoid duplication)
                viz_config_no_title = viz_config.copy()
                viz_config_no_title["title"] = ""
                fig = visualizer.create_visualization(viz_config_no_title, results_df_for_viz)

                content.append(html.Div(className="chart-container", children=[
                    html.H4(
                        chart_title,
                        className="chart-title",
                        **platform_attr  # Add platform as data attribute
                    ),
                    dcc.Graph(
                        figure=fig,
                        config={
                            'responsive': True,
                            'displayModeBar': True,  # Hide the mode bar
                            'scrollZoom': True,      # Disable scroll zoom
                            'doubleClick': False,     # Disable double click actions
                            'staticPlot': False       # Enable hover effects and interactivity
                        },
                        style={
                            'height': '400px',  # Reduced height
                            'width': '100%',
                            'maxHeight': '400px',  # Reduced max height
                            'marginBottom': '20px'  # Add margin at the bottom
                        }
                    )
                ]))
            except Exception as e:
                print(f"Error creating visualization: {e}")  # Log error
                content.append(dbc.Alert(f"Error creating visualization. Please check data format.", color="danger"))

        # Data Table (using reconstructed DataFrame)
        if not results_df_for_viz.empty:
            # Format numeric columns to 2 decimal places
            formatted_df = results_df_for_viz.copy()
            for col in formatted_df.columns:
                if formatted_df[col].dtype.kind in 'ifc':
                    # Format numbers with commas and 2 decimal places
                    formatted_df[col] = formatted_df[col].apply(
                        lambda x: f"{x:,.2f}" if isinstance(x, (int, float)) else x
                    )

            content.append(html.Div(className="data-table-section", children=[
                html.Div(className="data-table-header", children=[
                    html.H4("Data Table", className="data-table-title"),
                ]),
                # Use the formatted DataFrame here
                dbc.Table.from_dataframe(
                    formatted_df,
                    striped=True,
                    bordered=True,
                    hover=True,
                    responsive=True,
                    className="data-table"
                )
            ]))

        # Insights (with safer access from stored data)
        insights = latest_result_data.get("insights", [])

        # Get the selected metric type for context in insights
        metric_type_display = {
            "AVG": "Average",
            "SUM": "Total",
            "COUNT": "Count",
            "PERCENTAGE": "Percentage"
        }.get(selected_metric_type, "Average")

        # Create key insights content for the fixed section at the bottom
        key_insights_content = []
        if insights:
            # Format insights with the selected metric type context
            formatted_insights = []
            for insight in insights:
                # Replace generic metric references with the specific type
                formatted_insight = insight
                if selected_metric_type != "AVG":
                    formatted_insight = insight.replace("average", metric_type_display.lower())
                    formatted_insight = formatted_insight.replace("Average", metric_type_display)
                formatted_insights.append(formatted_insight)

            # Create adaptive insights layout with enhanced styling
            try:
                # Initialize adaptive insights component with current theme
                adaptive_insights = AdaptiveInsights(dark_mode=dark_mode)

                # Create adaptive layout based on insights complexity and relationships
                adaptive_layout = adaptive_insights.create_adaptive_insights(
                    formatted_insights,
                    query=latest_result_data.get("query"),
                    results=results_df_for_viz
                )

                # Add a visual separator between data table and insights
                key_insights_content = [
                    html.Div(className="insights-visual-separator"),
                    adaptive_layout
                ]
            except Exception as e:
                print(f"Error creating adaptive insights: {e}")
                # Fallback to simple layout
                key_insights_content = [
                    html.Div(className="insights-visual-separator"),
                    html.Div(className="insights-container", children=[
                        html.Div(className="insight-item", children=[
                            html.Span(insight, className="insight-text")
                        ]) for insight in formatted_insights
                    ])
                ]

        # Technical Details (ensure safe access from stored data)
        tech_details = []
        if latest_result_data:
            method = latest_result_data.get('method')
            proc_time = latest_result_data.get('processing_time')
            sql = latest_result_data.get('sql')
            if method: tech_details.append(html.P(f"Method: {method}"))
            if proc_time is not None: tech_details.append(html.P(f"Processing time: {proc_time:.3f} seconds"))
            if sql: tech_details.append(html.Div([html.Strong("SQL Query:"), dcc.Markdown(f"```sql\n{sql}\n```")]))

        if tech_details:
            content.append(dbc.Accordion([
                dbc.AccordionItem(tech_details, title="Technical Details", className="tech-details-accordion")
            ], start_collapsed=True, className="tech-details-accordion-container"))

        # Return both the visualization content and the key insights content
        return content, key_insights_content
