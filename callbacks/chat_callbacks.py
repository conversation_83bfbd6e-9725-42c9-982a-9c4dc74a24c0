"""
Chat-related callbacks for the Euranet Dashboard application.
This file contains callbacks for handling chat messages, suggestions, and loading states.
"""

import time
from datetime import datetime
import pandas as pd
import dash
from dash import callback, Input, Output, State, html, dcc
import dash_bootstrap_components as dbc

from constants import *
from components.query_processor import QueryProcessor
from components.suggestions import SuggestionGenerator

def register_chat_callbacks(db):
    """
    Register all chat-related callbacks.

    Args:
        db: The database connector instance.
    """
    # Initialize the query processor with Gemma and ADK integration
    use_gemma = True  # Set to False to disable Gemma
    use_adk = True    # Set to False to disable ADK
    query_processor = QueryProcessor(db, use_gemma=use_gemma, use_adk=use_adk)
    suggestion_generator = SuggestionGenerator(db)

    @callback(
        Output(CHAT_MESSAGES_CONTAINER_ID, "children"),
        Input(CHAT_HISTORY_STORE_ID, "data")
    )
    def update_chat_display(chat_data):
        """
        Update the chat messages display based on chat history.

        Args:
            chat_data: The chat history data from the store.

        Returns:
            list: A list of message components to display.
        """
        if not chat_data or 'messages' not in chat_data:
            return []

        message_components = []
        for msg in chat_data['messages']:
            is_user = msg["role"] == "user"
            msg_class = "user-message" if is_user else "assistant-message"

            # Use dcc.Markdown for assistant messages to render line breaks and potential markdown
            content_component = dcc.Markdown(msg["content"], className="message-content", dangerously_allow_html=True) \
                                if not is_user else html.Div(msg["content"], className="message-content")

            message_components.append(
                # Apply classes for styling via CSS
                html.Div(className=f"message {msg_class}", children=[
                    content_component,  # Use the appropriate component
                    html.Div(
                        datetime.fromtimestamp(msg["timestamp"]).strftime('%H:%M'),
                        className="message-timestamp"
                    )
                ])
            )
        return message_components

    @callback(
        Output(CHAT_HISTORY_STORE_ID, "data", allow_duplicate=True),
        Output(CHAT_INPUT_ID, "value"),
        Output(SUGGESTION_AREA_ID, "children"),
        Output(CURRENT_RESULT_STORE_ID, "data"),
        Output(LOADING_STATE_STORE_ID, "data"),
        Input(SEND_BUTTON_ID, "n_clicks"),
        Input(CHAT_INPUT_ID, "n_submit"),  # Track Enter key presses
        State(CHAT_INPUT_ID, "value"),
        State(CHAT_HISTORY_STORE_ID, "data"),
        prevent_initial_call=True
    )
    def handle_send_message(n_clicks, n_submit, input_value, chat_data_state):
        """
        Handle sending a message and update suggestions.

        Args:
            n_clicks: Number of times the send button has been clicked.
            n_submit: Number of times Enter key has been pressed.
            input_value: The text input value.
            chat_data_state: The current chat history data.

        Returns:
            tuple: Updated chat data, cleared input value, suggestion components,
                  result data, and loading state.
        """
        # Check if triggered by button click or Enter key press
        if (n_clicks is None and n_submit is None) or not input_value:
            # Return no_update for all outputs if condition not met
            return dash.no_update, "", dash.no_update, dash.no_update, dash.no_update

        # Set loading state to True
        loading_update = {'is_loading': True}

        # Create a copy to treat state as immutable
        chat_data = chat_data_state.copy() if chat_data_state else {'messages': []}
        if 'messages' not in chat_data:  # Ensure messages list exists
            chat_data['messages'] = []

        # Add user message
        user_message = {
            "role": "user",
            "content": input_value,
            "timestamp": time.time()
        }
        chat_data['messages'].append(user_message)

        # Process query using backend components
        assistant_message = {}
        processed_result_data = None  # To store serializable data
        try:
            start_time = time.time()
            # Ensure query_processor returns a dict, even on error within its methods
            result = query_processor.process_query(input_value)

            if not isinstance(result, dict):
                # Correct indentation for the raise statement
                raise ValueError("Query processing did not return expected result format.")

            result["processing_time"] = time.time() - start_time

            # Prepare serializable data for storage
            processed_result_data = result.copy()  # Start with a copy
            results_df = processed_result_data.get("results")

            # Convert DataFrame to dict if it exists
            if isinstance(results_df, pd.DataFrame):
                processed_result_data["results"] = results_df.to_dict('records')
            elif results_df is not None:  # Handle cases where it might be something else
                 print(f"Warning: 'results' key contains non-DataFrame data: {type(results_df)}")
                 # Decide how to handle non-DataFrame results, maybe convert to string or remove
                 processed_result_data["results"] = None  # Or str(results_df)

            # Format response content (simplified but safer access)
            response_content = f"Processed: '{input_value}'."  # Default response
            insights = processed_result_data.get("insights", [])  # Default to empty list

            # Check if we have Gemma-enhanced understanding
            gemma_enhanced = processed_result_data.get("gemma_enhanced", {})

            # Add Gemma's enhanced understanding if available
            if gemma_enhanced and gemma_enhanced.get("enhanced_understanding"):
                response_content += "\n\n**Gemma's Analysis:**\n"
                response_content += gemma_enhanced.get("enhanced_understanding")

            # Use the potentially converted results for length check
            processed_results = processed_result_data.get("results")
            if processed_results is not None and isinstance(processed_results, list) and len(processed_results) > 0:
                 response_content = f"Found {len(processed_results)} results for '{input_value}'."
            elif isinstance(results_df, pd.DataFrame) and not results_df.empty:  # Fallback check on original df
                 response_content = f"Found {len(results_df)} results for '{input_value}'."

            # Add insights with markdown formatting
            if insights:
                 response_content += "\n\n**Insights:**\n" + "\n* ".join(insights)  # Use markdown list

            # Clean up None/nan values in the final string for display
            display_content = response_content.replace(" None", " N/A").replace(" nan", " N/A")

            assistant_message = {
                "role": "assistant",
                "content": display_content,  # Use cleaned content for display
                "timestamp": time.time(),
                "data": processed_result_data  # Store potentially raw data
            }

        except Exception as e:
             # Log the full error for debugging
             print(f"Error during query processing or message formatting: {e}")
             # Provide a user-friendly error message
             assistant_message = {
                "role": "assistant",
                "content": f"Sorry, I encountered an error processing your request for '{input_value}'. Please try again or rephrase your query.",
                "timestamp": time.time(),
                "data": None  # Ensure data key exists but is None on error
             }

        # Append the assistant message (either success or error message)
        # Ensure chat_data['messages'] exists before appending
        if 'messages' not in chat_data:
            chat_data['messages'] = []
        chat_data['messages'].append(assistant_message)

        # --- Generate Suggestions ---
        suggestions = []
        suggestion_components = []
        try:
            current_results_for_suggestions = pd.DataFrame(processed_result_data.get("results")) if processed_result_data and processed_result_data.get("results") else None
            suggestions = suggestion_generator.generate_suggestions(
                chat_data['messages'],  # Pass full message history
                current_results_for_suggestions
            )
            if suggestions:
                 suggestion_components = [
                     html.P("Suggested Questions:", className="suggestion-title")
                 ] + [
                     dbc.Button(
                         suggestion,
                         id={"type": SUGGESTION_BUTTON_TYPE, "index": i},  # Pattern-matching ID
                         color="light",
                         className="suggestion-pill",
                         size="sm"
                     ) for i, suggestion in enumerate(suggestions)
                 ]
        except Exception as e:
            print(f"Error generating suggestions: {e}")
            suggestion_components = [dbc.Alert("Could not generate suggestions.", color="warning", duration=3000)]

        # Explicitly update current-result-store
        current_result_store_update = processed_result_data if assistant_message.get("data") else None

        # Set loading state back to False
        loading_update = {'is_loading': False}

        return chat_data, "", suggestion_components, current_result_store_update, loading_update

    @callback(
        Output(LOADING_INDICATOR_CONTAINER_ID, "children"),
        Input(LOADING_STATE_STORE_ID, "data")
    )
    def update_loading_indicator(loading_data):
        """
        Update the loading indicator based on loading state.

        Args:
            loading_data: The loading state data.

        Returns:
            dbc.Spinner or None: A spinner component if loading, otherwise None.
        """
        if loading_data and loading_data.get('is_loading'):
            return dbc.Spinner(size="sm", color="primary", className="loading-spinner")
        return None

    @callback(
        Output(CHAT_INPUT_ID, "value", allow_duplicate=True),
        Output(SEND_BUTTON_ID, "n_clicks"),  # Trigger send button click
        Input({"type": SUGGESTION_BUTTON_TYPE, "index": dash.ALL}, "n_clicks"),
        State({"type": SUGGESTION_BUTTON_TYPE, "index": dash.ALL}, "children"),
        State(SEND_BUTTON_ID, "n_clicks"),
        prevent_initial_call=True,
    )
    def handle_suggestion_click(n_clicks_list, suggestion_texts, current_send_clicks):
        """
        Handle clicks on suggestion buttons.

        Args:
            n_clicks_list: List of click counts for each suggestion button.
            suggestion_texts: List of text content for each suggestion button.
            current_send_clicks: Current click count for the send button.

        Returns:
            tuple: The selected suggestion text and updated send button click count.
        """
        ctx = dash.callback_context
        if not ctx.triggered_id or not any(n_clicks is not None and n_clicks > 0 for n_clicks in n_clicks_list):
            return dash.no_update, dash.no_update

        # Find which button was clicked
        button_id = ctx.triggered_id
        button_index = button_id["index"]
        clicked_suggestion_text = suggestion_texts[button_index]

        # Handle the case where current_send_clicks is None
        new_send_clicks = 1 if current_send_clicks is None else current_send_clicks + 1

        # Return the suggestion text and increment the send button's n_clicks
        return clicked_suggestion_text, new_send_clicks

    # Enter key handling is now integrated into the main send message callback
