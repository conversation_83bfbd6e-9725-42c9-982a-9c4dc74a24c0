# Testing Guide for Euranet Dashboard

## Setup and Launch

1. **VS Code Configuration**
   - Ensure MS Edge extension is installed in VS Code
   - Place `launch.json` in the `.vscode` directory
   - Configure Python environment:
     ```bash
     source venv_dash/bin/activate
     pip install -r requirements.txt
     ```

2. **Launch Application**
   - Press F5 or use Run -> Start Debugging
   - Select "Launch Edge against localhost"
   - The app will start and Edge will open automatically

## UI Testing Checklist

### Chat Interface
- [ ] Input field is responsive
- [ ] Send button works with both click and Enter key
- [ ] Messages appear with smooth animation
- [ ] Scroll behavior is smooth
- [ ] Code snippets are properly formatted
- [ ] Time stamps are correctly displayed
- [ ] Suggestions appear and are clickable

### Visualization Panel
- [ ] Charts render properly
- [ ] Chart interactions work (hover, zoom if enabled)
- [ ] Data tables are properly formatted
- [ ] Numbers are aligned and formatted consistently
- [ ] Insights section is readable
- [ ] Technical details are expandable

### Responsive Design
- [ ] Test at different window sizes
- [ ] Verify mobile layout (use Edge DevTools)
- [ ] Check that charts resize properly
- [ ] Verify input area remains accessible
- [ ] Test touch interactions

### Performance Testing
1. Open Edge DevTools (F12)
2. Go to Performance tab
3. Check:
   - [ ] Initial load time
   - [ ] Message response time
   - [ ] Chart rendering performance
   - [ ] Scrolling smoothness

### Edge DevTools Features

1. **Device Emulation**
   ```
   1. Press F12 to open DevTools
   2. Click Device Emulation (mobile icon)
   3. Test on different device presets
   ```

2. **Network Testing**
   ```
   1. Open Network tab
   2. Test with different network conditions
   3. Verify loading states appear
   ```

3. **Console Monitoring**
   ```
   1. Open Console tab
   2. Monitor for errors/warnings
   3. Check performance messages
   ```

4. **Elements Inspection**
   ```
   1. Use Element selector (Ctrl+Shift+C)
   2. Verify CSS classes are applied
   3. Check accessibility attributes
   ```

## Visual Regression Testing

1. **Baseline Screenshots**
   - Take screenshots of key states:
     - Initial load
     - With messages
     - With visualizations
     - Mobile view

2. **Compare Across Versions**
   ```bash
   # Store screenshots in tests/screenshots
   # Name format: feature_state_version.png
   # Example: chat_with_messages_v1.png
   ```

## Accessibility Testing

1. **Edge Accessibility Tools**
   - Open DevTools
   - Go to Accessibility tab
   - Run automated checks

2. **Manual Checks**
   - [ ] Color contrast meets WCAG standards
   - [ ] Keyboard navigation works
   - [ ] Screen reader compatibility
   - [ ] Focus indicators are visible

## Common Test Scenarios

1. **Basic Chat Flow**
   ```
   1. Enter a question
   2. Verify response
   3. Check visualization updates
   4. Test suggestion clicks
   ```

2. **Data Visualization**
   ```
   1. Generate different chart types
   2. Check data table sorting
   3. Verify number formatting
   4. Test export features
   ```

3. **Error Handling**
   ```
   1. Test invalid inputs
   2. Check error messages
   3. Verify recovery behavior
   ```

## Performance Metrics

Track these metrics in Edge DevTools:
- First Contentful Paint: < 1.5s
- Time to Interactive: < 3.0s
- Message Response Time: < 1.0s
- Chart Render Time: < 2.0s

## Debugging Tips

1. **Edge DevTools Shortcuts**
   - F12: Open DevTools
   - Ctrl+Shift+I: Toggle DevTools
   - Ctrl+Shift+M: Toggle device emulation

2. **Common Issues**
   ```
   - Charts not rendering: Check console for plotly errors
   - Slow responses: Monitor Network tab
   - Layout issues: Use Elements inspector
   ```

## Reporting Issues

Format bug reports as:
```
Environment:
- Edge Version: [version]
- Screen Size: [dimensions]
- OS: [operating system]

Steps to Reproduce:
1. [step 1]
2. [step 2]

Expected Result:
[what should happen]

Actual Result:
[what happened instead]

Screenshots:
[attach relevant screenshots]
```

## Continuous Testing

1. **Regular Checks**
   - Test after each CSS change
   - Verify on different screen sizes
   - Check performance metrics
   - Monitor error logs

2. **Automated Tests**
   - Consider adding Cypress or Selenium tests
   - Set up GitHub Actions for CI/CD
   - Implement screenshot comparison tests

Remember to test both light and dark themes, and verify all interactive elements work as expected.
