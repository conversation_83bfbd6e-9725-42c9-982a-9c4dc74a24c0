{"cells": [{"cell_type": "code", "execution_count": 17, "id": "c6f1fcda-11c4-4627-a2b5-b5dbfc580b0a", "metadata": {}, "outputs": [], "source": ["import streamlit as st\n", "from python_weather import Client\n", "import asyncio\n", "import spacy\n", "\n", "# Load spaCy model\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "# Function to get weather\n", "async def get_weather(location, intent):\n", "    async with Client() as client:\n", "        weather = await client.get(location)\n", "        \n", "        if intent == \"get_temperature\":\n", "            return (\n", "                weather.temperature,\n", "                weather.description,\n", "                weather.humidity,\n", "                weather.wind_speed,\n", "                weather.pressure,\n", "                weather.feels_like,\n", "                weather.precipitation,\n", "                weather.datetime.strftime(\"%Y-%m-%d\"),\n", "            )\n", "        elif intent == \"get_humidity\":\n", "            return weather.humidity, None, None, None, None, None, None, weather.datetime.strftime(\"%Y-%m-%d\")\n", "        elif intent == \"get_wind_speed\":\n", "            return weather.wind_speed, None, None, None, None, None, None, weather.datetime.strftime(\"%Y-%m-%d\")\n", "        elif intent == \"get_rain\":\n", "            return weather.precipitation, None, None, None, None, None, None, weather.datetime.strftime(\"%Y-%m-%d\")\n", "        else:\n", "            return None, None, None, None, None, None, None, None\n", "\n", "# Function to get intent and entities from user question\n", "def get_intent_and_entities(question):\n", "    doc = nlp(question)\n", "    intent = \"unknown\"\n", "    location = None\n", "\n", "\n", "    # Intent recognition using keywords and patterns\n", "    for token in doc:\n", "        if token.lemma_.lower() in [\"temperature\", \"weather\", \"hot\", \"cold\", \"warm\"]:\n", "            intent = \"get_temperature\"\n", "        elif token.lemma_.lower() in [\"humidity\"]:\n", "            intent = \"get_humidity\"\n", "        elif token.lemma_.lower() in [\"wind\", \"windy\"]:\n", "            intent = \"get_wind_speed\"\n", "        elif token.lemma_.lower() in [\"rain\"]:\n", "            intent = \"get_rain\"\n", "\n", "    # Extract entities\n", "    for ent in doc.ents:\n", "        if ent.label_ == \"GPE\":  # Geopolitical Entity (location)\n", "            location = ent.text\n", "\n", "    return intent, location\n", "\n", "# Main function\n", "def main():\n", "    st.title(\"Weather Query Bot\")\n", "\n", "    location = st.text_input(\"Enter a location:\", \"Hamburg\")  # Default to Hamburg\n", "    question = st.text_input(\"Ask a weather question:\")\n", "\n", "    if st.button(\"Get Weather\"):\n", "        if not location:\n", "            st.warning(\"Please enter a location.\")\n", "        else:\n", "            intent, _ = get_intent_and_entities(question.lower())\n", "            st.write(f\"Recognized intent: {intent}, Location: {location}\")\n", "            result, description, humidity, wind_speed, pressure, feels_like, precipitation, date_retrieved = asyncio.run(get_weather(location, intent))\n", "            if result is not None:\n", "                if intent == \"get_temperature\":\n", "                    st.write(f\"The current weather in {location} on {date_retrieved} is {result}°C with {description}.\")\n", "                    st.write(f\"Humidity: {humidity}%, Rain: {precipitation} mm, Wind Speed: {wind_speed} m/s, Pressure: {pressure} hPa, Feels Like: {feels_like}°C\")\n", "                elif intent == \"get_humidity\":\n", "                    st.write(f\"The humidity in {location} on {date_retrieved} is {result}%.\")\n", "                elif intent == \"get_wind_speed\":\n", "                    st.write(f\"The wind speed in {location} on {date_retrieved} is {result} m/s.\")\n", "                elif intent == \"get_rain\":\n", "                    st.write(f\"The rain in {location} on {date_retrieved} is {result} mm.\")\n", "            else:\n", "                st.write(\"I'm sorry, I didn't understand your question.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "07fd0a6d-9c95-4701-bbfd-639270d41e70", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}