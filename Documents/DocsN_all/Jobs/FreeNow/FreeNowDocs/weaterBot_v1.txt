
#--------------------------------------------v4.6TEMP with network running-- AI working with UI
import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai
import config
import subprocess
import os
import sys


def run_streamlit_app():
    # Your Streamlit app code goes here
    st.title("My Streamlit App")

    # Load spaCy model
    nlp = spacy.load("en_core_web_sm")
    
    
    # Set your OpenAI API key 
    openai.api_key = config.OPENAI_API_KEY
    
    
    # Function to get weather
    async def get_weather(location):
        async with Client() as client:
            weather = await client.get(location)
            # Extract necessary attributes and store them in a dictionary
            weather_data = {
                "temperature": weather.temperature,
                "description": weather.description,
                "datetime": weather.datetime,
                "feels_like": weather.feels_like,
                "humidity": weather.humidity,
                "wind_speed": weather.wind_speed,
                "kind": weather.kind,
                "uv": weather.ultraviolet,
                "daily_forecasts": []
            }
    
            for daily in weather.daily_forecasts:
                daily_forecast = {
                    "date": daily.date,
                    "highest_temperature": daily.highest_temperature,
                    "hourly_forecasts": daily.hourly_forecasts,
                    "locale": daily.locale,
                    "lowest_temperature": daily.lowest_temperature,
                    "moon_illumination": daily.moon_illumination,
                }
                weather_data["daily_forecasts"].append(daily_forecast)
    
            return weather_data
    
    
    # Function to generate weather response using GPT (updated)
    async def get_weather_response(location, question):
        weather_data = await get_weather(location)
        
        # Access the necessary values directly from the dictionary
        temperature = weather_data["temperature"]
        description = weather_data["description"]
        forecast_datetime = weather_data["datetime"]
        feels_like = weather_data["feels_like"]
        humidity = weather_data["humidity"]
        wind_speed = weather_data["wind_speed"]
        kind = weather_data["kind"]
        uv = weather_data["uv"]
    
        prompt = f"""
        You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.
    
        Location: {location}
        Temperature: {temperature}°C
        Description: {description}
        Date: {forecast_datetime}
        Feels Like: {feels_like}°C
        Humidity: {humidity}%
        Wind Speed: {wind_speed} km/h
        Kind: {kind}
        UV Index: {uv}
    
        Daily Forecasts:
        """
        
        for daily in weather_data['daily_forecasts']:
            prompt += f"""
            Date: {daily['date']}
            Highest Temperature: {daily['highest_temperature']}°C
            Lowest Temperature: {daily['lowest_temperature']}°C
            Moon Illumination: {daily['moon_illumination']}
            """
            
            # hourly forecasts
            for hourly in daily['hourly_forecasts']:
                prompt += f"""
                Time: {hourly.time}, Temperature: {hourly.temperature}°C, Description: {hourly.description}
                """
                
        prompt += f"""       
        Question: {question}
        Answer:
        """
    
        response = openai.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=150,
            n=1,
            stop=None,
            temperature=0.7,
        )
    
        return response.choices[0].message.content.strip()  # Updated response extraction
    
    
    # Main function
    def main():
        st.set_page_config(page_title="Weather Chatbot 🌤️", layout="wide")  # Set page config for wider layout
    
        # Weather Chatbot Header
        st.markdown(
            """
            <div id="weather-chatbot-header">
                <h1>Weather Chatbot 🌤️</h1>
                <div id="location-container">
                    <span id="current-location">Default location: Berlin</span> 
                    <i class="fa fa-info-circle" aria-hidden="true"></i>
                </div>
            </div>
            """,
            unsafe_allow_html=True,
        )
        
        # Initialize chat history
        if "messages" not in st.session_state:
            st.session_state.messages = []
            
        # Initialize location (default: Hamburg)
        if "location" not in st.session_state:
            st.session_state.location = "Berlin"
    
        # Display chat messages from history
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
    
        # User input (updated to handle location changes)
        if prompt := st.chat_input("Ask me about the weather..."):
            st.session_state.messages.append({"role": "user", "content": prompt})
            with st.chat_message("user"):
                st.markdown(prompt)
                
            # Location Change Detection (using spaCy for NER)
            doc = nlp(prompt)
            for ent in doc.ents:
                if ent.label_ == "GPE":  # GPE = Geopolitical Entity (location)
                    new_location = ent.text
                    st.session_state.location = new_location
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": f"Location changed to {new_location}. Here's the weather forecast:"
                    })
                    break  # Stop after the first location is found
        
            # Get weather response
            response = asyncio.run(get_weather_response(st.session_state.location, prompt))  # Use updated location
    
    
            # Append bot response to chat history
            st.session_state.messages.append({"role": "assistant", "content": response})
            with st.chat_message("assistant"):
                st.markdown(response)
    
            # Scroll to the bottom of the chat container after new message
            st.markdown(
                """
                <script>
                    const chat_container = document.querySelector('.css-ytpt3y.e1fqkh3o11');
                    if (chat_container) {
                        chat_container.scrollTop = chat_container.scrollHeight;
                    }
                </script>
                """,
                unsafe_allow_html=True,
            )
        
            # CSS for Fixed Header and Location Container
            st.markdown(
                """
                <style>
                    #weather-chatbot-header {
                        position: initial;
                        top: 0;
                        width: 100%;
                        z-index: 1000;
                        background-color: #222326;
                        padding: 5px;
                    }
                    #location-container {
                        margin-left: 20px; 
                    }
                    .stApp { 
                        margin-top: 0px; 
                    }
                    #current-location {
                      color:white;
                    }
                    #location-instruction {
                        color:white;
                    }
        
                    .css-ytpt3y.e1fqkh3o11 {
                        overflow-y: auto;
                        height: 300px; /* or any height that you prefer */
                    }
                    .st-emotion-cache {
                        width: 100%;
                        inset: 1%;
                        min-width: auto;
                        max-width: initial;
                    }
                </style>
                """,
                unsafe_allow_html=True,
            )
    


if __name__ == "__main__":
    # Get the script file path
    script_file = sys.argv[0]

    # Start the Streamlit app
    streamlit_process = subprocess.Popen(["streamlit", "run", script_file])

    # Start the local web server
    os.system("python -m http.server 8501")

    # Wait for the Streamlit process to finish
    streamlit_process.wait()


#--------------------------------------------v4.5 AI working with UI

import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai
import config


# Load spaCy model
nlp = spacy.load("en_core_web_sm")


# Set your OpenAI API key 
#openai.api_key = "********************************************************"
openai.api_key = config.	


# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        # Extract necessary attributes and store them in a dictionary
        weather_data = {
            "temperature": weather.temperature,
            "description": weather.description,
            "datetime": weather.datetime,
            "feels_like": weather.feels_like,
            "humidity": weather.humidity,
            "wind_speed": weather.wind_speed,
            "kind": weather.kind,
            "uv": weather.ultraviolet,
            "daily_forecasts": []
        }

        for daily in weather.daily_forecasts:
            daily_forecast = {
                "date": daily.date,
                "highest_temperature": daily.highest_temperature,
                "hourly_forecasts": daily.hourly_forecasts,
                "locale": daily.locale,
                "lowest_temperature": daily.lowest_temperature,
                "moon_illumination": daily.moon_illumination,
            }
            weather_data["daily_forecasts"].append(daily_forecast)

        return weather_data


# Function to generate weather response using GPT (updated)
async def get_weather_response(location, question):
    weather_data = await get_weather(location)
    
    # Access the necessary values directly from the dictionary
    temperature = weather_data["temperature"]
    description = weather_data["description"]
    forecast_datetime = weather_data["datetime"]
    feels_like = weather_data["feels_like"]
    humidity = weather_data["humidity"]
    wind_speed = weather_data["wind_speed"]
    kind = weather_data["kind"]
    uv = weather_data["uv"]

    prompt = f"""
    You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.

    Location: {location}
    Temperature: {temperature}°C
    Description: {description}
    Date: {forecast_datetime}
    Feels Like: {feels_like}°C
    Humidity: {humidity}%
    Wind Speed: {wind_speed} km/h
    Kind: {kind}
    UV Index: {uv}

    Daily Forecasts:
    """
    
    for daily in weather_data['daily_forecasts']:
        prompt += f"""
        Date: {daily['date']}
        Highest Temperature: {daily['highest_temperature']}°C
        Lowest Temperature: {daily['lowest_temperature']}°C
        Moon Illumination: {daily['moon_illumination']}
        """
        
        # hourly forecasts
        for hourly in daily['hourly_forecasts']:
            prompt += f"""
            Time: {hourly.time}, Temperature: {hourly.temperature}°C, Description: {hourly.description}
            """
            
    prompt += f"""       
    Question: {question}
    Answer:
    """

    response = openai.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=150,
        n=1,
        stop=None,
        temperature=0.7,
    )

    return response.choices[0].message.content.strip()  # Updated response extraction


# Main function
def main():
    st.set_page_config(page_title="Weather Chatbot 🌤️", layout="wide")  # Set page config for wider layout

    st.title("Weather Chatbot 🌤️")  # Added emoji to the title

    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Display chat messages from history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # User input
    if prompt := st.chat_input("Ask me about the weather..."):
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Get location from the user's message (you'll need to implement this)
        location = "Hamburg"  # Replace with your location extraction logic

        # Get weather response
        response = asyncio.run(get_weather_response(location, prompt))

        # Append bot response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})
        with st.chat_message("assistant"):
            st.markdown(response)

if __name__ == "__main__":
    main()

#--------------------------------------------v4.4.1 AI working with config API
import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai
import config


# Load spaCy model
nlp = spacy.load("en_core_web_sm")


# Set your OpenAI API key 
#openai.api_key = "********************************************************"
openai.api_key = config.OPENAI_API_KEY


# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        # Extract necessary attributes and store them in a dictionary
        weather_data = {
            "temperature": weather.temperature,
            "description": weather.description,
            "datetime": weather.datetime,
            "feels_like": weather.feels_like,
            "humidity": weather.humidity,
            "wind_speed": weather.wind_speed,
            "kind": weather.kind,
            "uv": weather.ultraviolet,
            "daily_forecasts": []
        }

        for daily in weather.daily_forecasts:
            daily_forecast = {
                "date": daily.date,
                "highest_temperature": daily.highest_temperature,
                "hourly_forecasts": daily.hourly_forecasts,
                "locale": daily.locale,
                "lowest_temperature": daily.lowest_temperature,
                "moon_illumination": daily.moon_illumination,
            }
            weather_data["daily_forecasts"].append(daily_forecast)

        return weather_data


# Function to generate weather response using GPT (updated)
async def get_weather_response(location, question):
    weather_data = await get_weather(location)
    
    # Access the necessary values directly from the dictionary
    temperature = weather_data["temperature"]
    description = weather_data["description"]
    forecast_datetime = weather_data["datetime"]
    feels_like = weather_data["feels_like"]
    humidity = weather_data["humidity"]
    wind_speed = weather_data["wind_speed"]
    kind = weather_data["kind"]
    uv = weather_data["uv"]

    prompt = f"""
    You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.

    Location: {location}
    Temperature: {temperature}°C
    Description: {description}
    Date: {forecast_datetime}
    Feels Like: {feels_like}°C
    Humidity: {humidity}%
    Wind Speed: {wind_speed} km/h
    Kind: {kind}
    UV Index: {uv}

    Daily Forecasts:
    """
    
    for daily in weather_data['daily_forecasts']:
        prompt += f"""
        Date: {daily['date']}
        Highest Temperature: {daily['highest_temperature']}°C
        Lowest Temperature: {daily['lowest_temperature']}°C
        Moon Illumination: {daily['moon_illumination']}
        """

    prompt += f"""       
    Question: {question}
    Answer:
    """

    response = openai.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=150,
        n=1,
        stop=None,
        temperature=0.7,
    )

    return response.choices[0].message.content.strip()  # Updated response extraction


# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")
    question = st.text_input("Ask a weather question:")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            response = asyncio.run(get_weather_response(location, question))
            st.write(response)

if __name__ == "__main__":
    main()
    
    
#--------------------------------------------v4.4 AI working with hardcoded API
import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai


# Load spaCy model
nlp = spacy.load("en_core_web_sm")


# Set your OpenAI API key from environment variables
openai.api_key = "********************************************************"

# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        # Extract necessary attributes and store them in a dictionary
        weather_data = {
            "temperature": weather.temperature,
            "description": weather.description,
            "datetime": weather.datetime,
            "feels_like": weather.feels_like,
            "humidity": weather.humidity,
            "wind_speed": weather.wind_speed,
            "kind": weather.kind,
            "uv": weather.ultraviolet,
            "daily_forecasts": []
        }

        for daily in weather.daily_forecasts:
            daily_forecast = {
                "date": daily.date,
                "highest_temperature": daily.highest_temperature,
                "hourly_forecasts": daily.hourly_forecasts,
                "locale": daily.locale,
                "lowest_temperature": daily.lowest_temperature,
                "moon_illumination": daily.moon_illumination,
            }
            weather_data["daily_forecasts"].append(daily_forecast)

        return weather_data


# Function to generate weather response using GPT (updated)
async def get_weather_response(location, question):
    weather_data = await get_weather(location)
    
    # Access the necessary values directly from the dictionary
    temperature = weather_data["temperature"]
    description = weather_data["description"]
    forecast_datetime = weather_data["datetime"]
    feels_like = weather_data["feels_like"]
    humidity = weather_data["humidity"]
    wind_speed = weather_data["wind_speed"]
    kind = weather_data["kind"]
    uv = weather_data["uv"]

    prompt = f"""
    You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.

    Location: {location}
    Temperature: {temperature}°C
    Description: {description}
    Date: {forecast_datetime}
    Feels Like: {feels_like}°C
    Humidity: {humidity}%
    Wind Speed: {wind_speed} km/h
    Kind: {kind}
    UV Index: {uv}

    Daily Forecasts:
    """
    
    for daily in weather_data['daily_forecasts']:
        prompt += f"""
        Date: {daily['date']}
        Highest Temperature: {daily['highest_temperature']}°C
        Lowest Temperature: {daily['lowest_temperature']}°C
        Moon Illumination: {daily['moon_illumination']}
        """

    prompt += f"""       
    Question: {question}
    Answer:
    """

    response = openai.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=150,
        n=1,
        stop=None,
        temperature=0.7,
    )

    return response.choices[0].message.content.strip()  # Updated response extraction


# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")
    question = st.text_input("Ask a weather question:")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            response = asyncio.run(get_weather_response(location, question))
            st.write(response)

if __name__ == "__main__":
    main()
    
    
#--------------------------------------------v4.3 working with all weather attribute

import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai
from datetime import datetime

# Load spaCy model
nlp = spacy.load("en_core_web_sm")

# Set your OpenAI API key
openai.api_key = "********************************************************"  # Replace with your actual API key


# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)

        # Get the current weather
        temperature = weather.temperature
        description = weather.description
        forecast_datetime = weather.datetime
        feels_like = weather.feels_like
        humidity = weather.humidity
        wind_speed = weather.wind_speed
        kind = weather.kind
        uv = weather.ultraviolet

        # Get the daily forecast
        for daily in weather.daily_forecasts:
            date = daily.date
            highest_temperature = daily.highest_temperature
            hourly_forecasts = daily.hourly_forecasts
            locale = daily.locale
            lowest_temperature = daily.lowest_temperature
            moon_illumination = daily.moon_illumination

        return temperature, description, forecast_datetime, feels_like, humidity, wind_speed, kind, uv, date, highest_temperature, hourly_forecasts, locale, lowest_temperature, moon_illumination
# Function to get intent and entities from user question
def get_intent_and_entities(question):
    doc = nlp(question)
    intent = "unknown"
    location = None
    forecast_datetime = None
    feels_like = None
    humidity = None
    wind_speed = None
    kind = None
    uv = None
    date = None
    highest_temperature = None
    hourly_forecasts = None
    locale = None
    lowest_temperature = None
    moon_illumination = None

    # Intent recognition using keywords and patterns
    for token in doc:
        if token.lemma_.lower() in ["temperature", "weather", "hot", "cold", "warm"]:
            intent = "get_temperature"
        elif token.lemma_.lower() in ["forecast", "prediction", "outlook", "future"]:
            intent = "get_forecast"

    # Extract entities
    for ent in doc.ents:
        if ent.label_ == "GPE":  # Geopolitical Entity (location)
            location = ent.text

    return intent, location, forecast_datetime, feels_like, humidity, wind_speed, kind, uv, date, highest_temperature, hourly_forecasts, locale, lowest_temperature, moon_illumination

# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")  # Default to Hamburg
    question = st.text_input("Ask a weather question:", "Temperature")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            intent, _, forecast_datetime, feels_like, humidity, wind_speed, kind, uv, date, highest_temperature, hourly_forecasts, locale, lowest_temperature, moon_illumination = get_intent_and_entities(question.lower())
            st.write(f"Recognized intent: {intent}, Location: {location}")
            if intent in ["get_temperature", "get_forecast"]:
                temperature, description, forecast_datetime, feels_like, humidity, wind_speed, kind, uv, date, highest_temperature, hourly_forecasts, locale, lowest_temperature, moon_illumination = asyncio.run(get_weather(location))
                if temperature is not None:
                    if intent == "get_temperature":
                        current_date = datetime.strptime(str(forecast_datetime), "%Y-%m-%d %H:%M:%S").date() if forecast_datetime else datetime.now().date()
                        st.write(f"The current temperature in {location} on {current_date} is {temperature}°C with {description}.datetime-{forecast_datetime}, feels like-{feels_like}, humidity-{humidity}%, wind-{wind_speed} km, kind-{kind}, UV-{uv}")
                        st.write(f"forecast date- {date},highest_temperature- {highest_temperature},hourly_forecasts- {hourly_forecasts},locale- {locale},lowest_temperature- {lowest_temperature},moon_illumination- {moon_illumination}")
                    else:
                        st.write(f"The forecast for {location} is {temperature}°C with {description}.")
            else:
                st.write("I'm sorry, I didn't understand your question.")

if __name__ == "__main__":
    main()




#---------------------------------------------------v4.2 without AI

import streamlit as st
from python_weather import Client
import asyncio
import spacy
from datetime import datetime

# Load spaCy model
nlp = spacy.load("en_core_web_sm")

# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)

        # Get the current weather
        temperature = weather.temperature
        description = weather.description
        forecast_datetime = weather.datetime

        return temperature, description, forecast_datetime

# Function to get intent and entities from user question
def get_intent_and_entities(question):
    doc = nlp(question)
    intent = "unknown"
    location = None


    # Intent recognition using keywords and patterns
    for token in doc:
        if token.lemma_.lower() in ["temperature", "weather", "hot", "cold", "warm"]:
            intent = "get_temperature"
        elif token.lemma_.lower() in ["forecast", "prediction", "outlook", "future"]:
            intent = "get_forecast"

    # Extract entities
    for ent in doc.ents:
        if ent.label_ == "GPE":  # Geopolitical Entity (location)
            location = ent.text

    return intent, location

# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")  # Default to Hamburg
    question = st.text_input("Ask a weather question:")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            intent, _ = get_intent_and_entities(question.lower())
            st.write(f"Recognized intent: {intent}, Location: {location}")
            if intent in ["get_temperature", "get_forecast"]:
                temperature, description, forecast_datetime = asyncio.run(get_weather(location))
                if temperature is not None:
                    if intent == "get_temperature":
                        current_date = datetime.strptime(str(forecast_datetime), "%Y-%m-%d %H:%M:%S").date() if forecast_datetime else datetime.now().date()
                        st.write(f"The current temperature in {location} on {current_date} is {temperature}°C with {description}.")
                    else:
                        st.write(f"The forecast for {location} is {temperature}°C with {description}.")
            else:
                st.write("I'm sorry, I didn't understand your question.")

if __name__ == "__main__":
    main()



#--------------------------------------------v4.1 with openAI
import streamlit as st
from python_weather import Client
import asyncio
import spacy
import openai

# Load spaCy model
nlp = spacy.load("en_core_web_sm")

# Set your OpenAI API key
openai.api_key = "********************************************************"  # Replace with your actual API key

# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        return weather.temperature, weather.description, weather.datetime

# Function to get intent and entities from user question
def get_intent_and_entities(question):
    doc = nlp(question)
    intent = "unknown"
    location = None
    date = None

    # Improved intent recognition:
    if any(token.lemma_.lower() in ["temperature", "weather", "hot", "cold", "warm"] for token in doc):
        intent = "get_temperature"
    elif any(token.lemma_.lower() in ["forecast", "prediction", "outlook", "future"] for token in doc):
        intent = "get_forecast"
    elif any(token.lemma_.lower() in ["humidity"] for token in doc):
        intent = "get_humidity"
    elif any(token.lemma_.lower() in ["wind", "windy"] for token in doc):
        intent = "get_wind_speed"
    elif any(token.lemma_.lower() in ["rain"] for token in doc):
        intent = "get_rain"

    # Extract entities
    for ent in doc.ents:
        if ent.label_ == "GPE":  # Geopolitical Entity (location)
            location = ent.text
        elif ent.label_ == "DATE":
            date = ent.text

    return intent, location, date


# Function to get weather information and generate response using GPT
async def get_weather_response(location, question):
    weather_data = await get_weather(location)
    temperature, description, date_retrieved = weather_data

    prompt = f"""
    You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.

    Location: {location}
    Temperature: {temperature}°C
    Description: {description}
    Date: {date_retrieved}

    Question: {question}
    Answer:
    """

    response = openai.ChatCompletion.create(  # Use ChatCompletion instead of Completion
        model="gpt-3.5-turbo",  # Use a suitable GPT model
        messages=[
            {"role": "system", "content": prompt}
        ],
        max_tokens=150,  # Adjust as needed
        n=1,
        stop=None,
        temperature=0.7,  # Adjust for creativity
    )

    return response['choices'][0]['message']['content'].strip()  # Updated response extraction

# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")  # Default to Hamburg
    question = st.text_input("Ask a weather question:")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            intent, _, date = get_intent_and_entities(question.lower())  
            st.write(f"Recognized intent: {intent}, Location: {location}, Date: {date}")  
            if intent in ["get_temperature", "get_forecast"]:
                temperature, description = asyncio.run(get_weather(location))
                if temperature is not None:
                    if intent == "get_temperature":
                        st.write(f"The current temperature in {location} is {temperature}°C with {description}.")
                    else:
                        st.write(f"The forecast for {location} on {date} is {temperature}°C with {description}.")
            else:
                st.write("I'm sorry, I didn't understand your question.")


if __name__ == "__main__":
    main()


#--------------------------------------------------------------------------v4 working

#!/usr/bin/env python
# coding: utf-8

# In[18]:


import streamlit as st
from python_weather import Client
import asyncio
import spacy

# Load spaCy model
nlp = spacy.load("en_core_web_sm")

# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        return weather.temperature, weather.description, weather.datetime.strftime("%Y-%m-%d")

# Function to get intent and entities from user question
def get_intent_and_entities(question):
    doc = nlp(question)
    intent = "unknown"
    location = None
    date = None

    # Improved intent recognition:
    if any(token.lemma_.lower() in ["temperature", "weather", "hot", "cold", "warm"] for token in doc):
        intent = "get_temperature"
    elif any(token.lemma_.lower() in ["forecast", "prediction", "outlook", "future"] for token in doc):
        intent = "get_forecast"
    else:
        intent = "unknown"

    # Extract location and date entities (unchanged)
    for ent in doc.ents:
        if ent.label_ == "GPE":  # Geopolitical Entity (location)
            location = ent.text
        elif ent.label_ == "DATE":
            date = ent.text

    return intent, location, date

# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:", "Hamburg")  # Default to Hamburg
    question = st.text_input("Ask a weather question:")

    if st.button("Get Weather"):
        if not location:
            st.warning("Please enter a location.")
        else:
            intent, _, date = get_intent_and_entities(question.lower())
            st.write(f"Recognized intent: {intent}, Location: {location}, Date: {date}")
            if intent in ["get_temperature", "get_forecast"]:
                temperature, description, date_retrieved = asyncio.run(get_weather(location))  # Get date_retrieved
                if temperature is not None:
                    if intent == "get_temperature":
                        st.write(f"The current temperature in {location} on {date_retrieved} is {temperature}°C with {description}.")  # Use date_retrieved
                    else:
                        st.write(f"The forecast for {location} is {temperature}°C with {description}.")  # We don't have the forecast date yet
            else:
                st.write("I'm sorry, I didn't understand your question.")

if __name__ == "__main__":
    main()



#-------------------------------------------------------------------------v3 final working
import streamlit as st
from python_weather import Client
import asyncio

# Function to get weather
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        return weather.temperature, weather.description


# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:")

    if st.button("Get Weather"):
        if location:
            temperature, description = asyncio.run(get_weather(location))
            st.write(f"The current temperature in {location} is {temperature}°C")
            st.write(f"Current weather conditions: {description}")
        else:
            st.warning("Please enter a location.")

if __name__ == "__main__":
    main()

---------v1
import streamlit as st
from streamlit.components.v1 import html
from python_weather import Client

st.title("Weather Query Bot")
location = st.text_input("Enter a location:")
question = st.text_input("Ask a weather question:")

if st.button("Get Weather"):
    client = Client()
    weather = client.get(location)
    current_temp = weather.current.temperature
    st.write(f"The current temperature in {location} is {current_temp}°C")

# Get the Streamlit app URL (replace with your actual URL)
app_url = "http://***********:8501/" 

# Embed the Streamlit app (Updated)
with st.container():
    st.components.v1.html(  # <-- Use st.components.v1.html
        f"""
        <iframe src="{app_url}" width="800" height="600">
        </iframe>
        """,
        height=600,
    )
    
    ----v2
    
import streamlit as st
from python_weather import Client
import asyncio

# Function to get weather (unchanged)
async def get_weather(location):
    async with Client() as client:
        weather = await client.get(location)
        return weather.current.temperature

# Main function
def main():
    st.title("Weather Query Bot")

    location = st.text_input("Enter a location:")
    question = st.text_input("Ask a weather question:")  # Not used yet

    if st.button("Get Weather"):
        if location:  
            temperature = asyncio.run(get_weather(location))
            # Display the temperature in the notebook directly
            st.write(f"The current temperature in {location} is {temperature}°C")
        else:
            st.warning("Please enter a location.")

if __name__ == "__main__":
    main()
