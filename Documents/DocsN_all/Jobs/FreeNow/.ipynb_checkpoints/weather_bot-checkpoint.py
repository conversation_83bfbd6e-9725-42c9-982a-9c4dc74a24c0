{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dfe7bd82-e6fb-4892-b09b-ee035703b619", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting streamlit\n", "  Downloading streamlit-1.35.0-py2.py3-none-any.whl.metadata (8.5 kB)\n", "Collecting python-weather\n", "  Downloading python_weather-2.0.3-py3-none-any.whl.metadata (3.7 kB)\n", "Collecting altair<6,>=4.0 (from streamlit)\n", "  Downloading altair-5.3.0-py3-none-any.whl.metadata (9.2 kB)\n", "Collecting blinker<2,>=1.0.0 (from streamlit)\n", "  Downloading blinker-1.8.2-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting cachetools<6,>=4.0 (from streamlit)\n", "  Downloading cachetools-5.3.3-py3-none-any.whl.metadata (5.3 kB)\n", "Collecting click<9,>=7.0 (from streamlit)\n", "  Downloading click-8.1.7-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting numpy<2,>=1.19.3 (from streamlit)\n", "  Downloading numpy-1.26.4-cp312-cp312-macosx_11_0_arm64.whl.metadata (61 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.1/61.1 kB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging<25,>=16.8 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from streamlit) (24.0)\n", "Collecting pandas<3,>=1.3.0 (from streamlit)\n", "  Downloading pandas-2.2.2-cp312-cp312-macosx_11_0_arm64.whl.metadata (19 kB)\n", "Collecting pillow<11,>=7.1.0 (from streamlit)\n", "  Downloading pillow-10.3.0-cp312-cp312-macosx_11_0_arm64.whl.metadata (9.2 kB)\n", "Collecting protobuf<5,>=3.20 (from streamlit)\n", "  Downloading protobuf-4.25.3-cp37-abi3-macosx_10_9_universal2.whl.metadata (541 bytes)\n", "Collecting pyarrow>=7.0 (from streamlit)\n", "  Downloading pyarrow-16.1.0-cp312-cp312-macosx_11_0_arm64.whl.metadata (3.0 kB)\n", "Requirement already satisfied: requests<3,>=2.27 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from streamlit) (2.32.3)\n", "Collecting rich<14,>=10.14.0 (from streamlit)\n", "  Downloading rich-13.7.1-py3-none-any.whl.metadata (18 kB)\n", "Collecting tenacity<9,>=8.1.0 (from streamlit)\n", "  Downloading tenacity-8.3.0-py3-none-any.whl.metadata (1.2 kB)\n", "Collecting toml<2,>=0.10.1 (from streamlit)\n", "  Downloading toml-0.10.2-py2.py3-none-any.whl.metadata (7.1 kB)\n", "Collecting typing-extensions<5,>=4.3.0 (from streamlit)\n", "  Downloading typing_extensions-4.12.1-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting gitpython!=3.1.19,<4,>=3.0.7 (from streamlit)\n", "  Downloading GitPython-3.1.43-py3-none-any.whl.metadata (13 kB)\n", "Collecting pydeck<1,>=0.8.0b4 (from streamlit)\n", "  Downloading pydeck-0.9.1-py2.py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: tornado<7,>=6.0.3 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from streamlit) (6.4)\n", "Collecting aiohttp==3.9.5 (from python-weather)\n", "  Downloading aiohttp-3.9.5-cp312-cp312-macosx_11_0_arm64.whl.metadata (7.5 kB)\n", "Collecting aiosignal>=1.1.2 (from aiohttp==3.9.5->python-weather)\n", "  Downloading aiosignal-1.3.1-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs>=17.3.0 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from aiohttp==3.9.5->python-weather) (23.2.0)\n", "Collecting frozenlist>=1.1.1 (from aiohttp==3.9.5->python-weather)\n", "  Downloading frozenlist-1.4.1-cp312-cp312-macosx_11_0_arm64.whl.metadata (12 kB)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp==3.9.5->python-weather)\n", "  Downloading multidict-6.0.5-cp312-cp312-macosx_11_0_arm64.whl.metadata (4.2 kB)\n", "Collecting yarl<2.0,>=1.0 (from aiohttp==3.9.5->python-weather)\n", "  Downloading yarl-1.9.4-cp312-cp312-macosx_11_0_arm64.whl.metadata (31 kB)\n", "Requirement already satisfied: jinja2 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from altair<6,>=4.0->streamlit) (3.1.4)\n", "Requirement already satisfied: jsonschema>=3.0 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from altair<6,>=4.0->streamlit) (4.22.0)\n", "Collecting toolz (from altair<6,>=4.0->streamlit)\n", "  Downloading toolz-0.12.1-py3-none-any.whl.metadata (5.1 kB)\n", "Collecting gitdb<5,>=4.0.1 (from gitpython!=3.1.19,<4,>=3.0.7->streamlit)\n", "  Downloading gitdb-4.0.11-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from pandas<3,>=1.3.0->streamlit) (2.9.0.post0)\n", "Collecting pytz>=2020.1 (from pandas<3,>=1.3.0->streamlit)\n", "  Downloading pytz-2024.1-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas<3,>=1.3.0->streamlit)\n", "  Downloading tzdata-2024.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from requests<3,>=2.27->streamlit) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from requests<3,>=2.27->streamlit) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from requests<3,>=2.27->streamlit) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from requests<3,>=2.27->streamlit) (2024.6.2)\n", "Collecting markdown-it-py>=2.2.0 (from rich<14,>=10.14.0->streamlit)\n", "  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from rich<14,>=10.14.0->streamlit) (2.18.0)\n", "Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit)\n", "  Downloading smmap-5.0.1-py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from jinja2->altair<6,>=4.0->streamlit) (2.1.5)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (2023.12.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.35.1)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.18.1)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich<14,>=10.14.0->streamlit)\n", "  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: six>=1.5 in /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas<3,>=1.3.0->streamlit) (1.16.0)\n", "Downloading streamlit-1.35.0-py2.py3-none-any.whl (8.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.6/8.6 MB\u001b[0m \u001b[31m19.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading python_weather-2.0.3-py3-none-any.whl (12 kB)\n", "Downloading aiohttp-3.9.5-cp312-cp312-macosx_11_0_arm64.whl (392 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m392.4/392.4 kB\u001b[0m \u001b[31m20.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading altair-5.3.0-py3-none-any.whl (857 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m857.8/857.8 kB\u001b[0m \u001b[31m17.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading blinker-1.8.2-py3-none-any.whl (9.5 kB)\n", "Downloading cachetools-5.3.3-py3-none-any.whl (9.3 kB)\n", "Downloading click-8.1.7-py3-none-any.whl (97 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m97.9/97.9 kB\u001b[0m \u001b[31m14.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading GitPython-3.1.43-py3-none-any.whl (207 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.3/207.3 kB\u001b[0m \u001b[31m15.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading numpy-1.26.4-cp312-cp312-macosx_11_0_arm64.whl (13.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.7/13.7 MB\u001b[0m \u001b[31m22.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading pandas-2.2.2-cp312-cp312-macosx_11_0_arm64.whl (11.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.3/11.3 MB\u001b[0m \u001b[31m22.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading pillow-10.3.0-cp312-cp312-macosx_11_0_arm64.whl (3.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m21.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading protobuf-4.25.3-cp37-abi3-macosx_10_9_universal2.whl (394 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m394.2/394.2 kB\u001b[0m \u001b[31m17.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyarrow-16.1.0-cp312-cp312-macosx_11_0_arm64.whl (26.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m26.0/26.0 MB\u001b[0m \u001b[31m21.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading pydeck-0.9.1-py2.py3-none-any.whl (6.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.9/6.9 MB\u001b[0m \u001b[31m22.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading rich-13.7.1-py3-none-any.whl (240 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m240.7/240.7 kB\u001b[0m \u001b[31m15.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tenacity-8.3.0-py3-none-any.whl (25 kB)\n", "Downloading toml-0.10.2-py2.py3-none-any.whl (16 kB)\n", "Downloading typing_extensions-4.12.1-py3-none-any.whl (37 kB)\n", "Downloading aiosignal-1.3.1-py3-none-any.whl (7.6 kB)\n", "Downloading frozenlist-1.4.1-cp312-cp312-macosx_11_0_arm64.whl (51 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.9/51.9 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gitdb-4.0.11-py3-none-any.whl (62 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.7/62.7 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m87.5/87.5 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading multidict-6.0.5-cp312-cp312-macosx_11_0_arm64.whl (29 kB)\n", "Downloading pytz-2024.1-py2.py3-none-any.whl (505 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m505.5/505.5 kB\u001b[0m \u001b[31m20.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tzdata-2024.1-py2.py3-none-any.whl (345 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m345.4/345.4 kB\u001b[0m \u001b[31m15.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading yarl-1.9.4-cp312-cp312-macosx_11_0_arm64.whl (79 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.4/79.4 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading toolz-0.12.1-py3-none-any.whl (56 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.1/56.1 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Downloading smmap-5.0.1-py3-none-any.whl (24 kB)\n", "Installing collected packages: pytz, tzdata, typing-extensions, toolz, toml, tenacity, smmap, protobuf, pillow, numpy, multidict, mdurl, frozenlist, click, cachetools, blinker, yarl, pydeck, pyarrow, pandas, markdown-it-py, gitdb, aiosignal, rich, gitpython, aiohttp, python-weather, altair, streamlit\n", "Successfully installed aiohttp-3.9.5 aiosignal-1.3.1 altair-5.3.0 blinker-1.8.2 cachetools-5.3.3 click-8.1.7 frozenlist-1.4.1 gitdb-4.0.11 gitpython-3.1.43 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.0.5 numpy-1.26.4 pandas-2.2.2 pillow-10.3.0 protobuf-4.25.3 pyarrow-16.1.0 pydeck-0.9.1 python-weather-2.0.3 pytz-2024.1 rich-13.7.1 smmap-5.0.1 streamlit-1.35.0 tenacity-8.3.0 toml-0.10.2 toolz-0.12.1 typing-extensions-4.12.1 tzdata-2024.1 yarl-1.9.4\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install streamlit python-weather"]}, {"cell_type": "code", "execution_count": 2, "id": "d30aee26-6ab5-48f9-ab24-e9bbc47cc8e3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-06-05 20:30:30.090 \n", "  \u001b[33m\u001b[1mWarning:\u001b[0m to view this Streamlit app on a browser, run it with the following\n", "  command:\n", "\n", "    streamlit run /Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/ipykernel_launcher.py [ARGUMENTS]\n", "2024-06-05 20:30:30.091 Session state does not function when running a script without `streamlit run`\n"]}], "source": ["import streamlit as st\n", "from python_weather import Client\n", "\n", "st.title(\"Weather Query Bot\")\n", "location = st.text_input(\"Enter a location:\")\n", "question = st.text_input(\"Ask a weather question:\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}