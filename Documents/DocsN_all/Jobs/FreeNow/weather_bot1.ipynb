{"cells": [{"cell_type": "code", "execution_count": 12, "id": "687eb8a5-03c3-4f72-8c09-24382292b091", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["environ({'__CFBundleIdentifier': 'com.apple.Terminal', 'TMPDIR': '/var/folders/sr/npj5wkss0jz1x9rrswqx3p340000gn/T/', 'XPC_FLAGS': '0x0', 'TERM': 'xterm-color', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.uHmCFYGybU/Listeners', 'XPC_SERVICE_NAME': '0', 'TERM_PROGRAM': 'Apple_Terminal', 'TERM_PROGRAM_VERSION': '453', 'TERM_SESSION_ID': '29781BD9-4B7A-48BB-BC7E-B76C2FB454F3', 'SHELL': '/bin/zsh', 'HOME': '/Users/<USER>', 'LOGNAME': 'deepakbatham', 'USER': 'deepakbatham', 'PATH': '/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin', 'SHLVL': '1', 'PWD': '/Users/<USER>', 'OLDPWD': '/Users/<USER>', 'LC_CTYPE': 'UTF-8', '_': '/Library/Frameworks/Python.framework/Versions/3.12/bin/jupyter', '__CF_USER_TEXT_ENCODING': '0x1F5:0x0:0x0', 'JPY_SESSION_NAME': '/Users/<USER>/Documents/DocsN_all/Jobs/FreeNow/weather_bot1.ipynb', 'JPY_PARENT_PID': '73645', 'PYDEVD_USE_FRAME_EVAL': 'NO', 'CLICOLOR': '1', 'FORCE_COLOR': '1', 'CLICOLOR_FORCE': '1', 'PAGER': 'cat', 'GIT_PAGER': 'cat', 'MPLBACKEND': 'Agg', 'OPENAI_API_KEY': '********************************************************'})\n"]}], "source": ["\n", "import streamlit as st\n", "from python_weather import Client\n", "import asyncio\n", "import spacy\n", "import openai\n", "import config\n", "import os  # Import the 'os' module for environment variables\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(dotenv_path='/Users/<USER>/Documents/DocsN_all/Jobs/FreeNow/config.env') \n", "print(os.environ)\n", "\n", "# Load spaCy model\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "# Get OpenAI API key from environment variable\n", "openai.api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "# Check if the API key is set\n", "if not openai.api_key:\n", "    raise ValueError(\"OPENAI_API_KEY environment variable not set.\")\n", "\n", "\n", "# Function to get weather\n", "async def get_weather(location):\n", "    async with Client() as client:\n", "        weather = await client.get(location)\n", "        # Extract necessary attributes and store them in a dictionary\n", "        weather_data = {\n", "            \"temperature\": weather.temperature,\n", "            \"description\": weather.description,\n", "            \"datetime\": weather.datetime,\n", "            \"feels_like\": weather.feels_like,\n", "            \"humidity\": weather.humidity,\n", "            \"wind_speed\": weather.wind_speed,\n", "            \"kind\": weather.kind,\n", "            \"uv\": weather.ultraviolet,\n", "            \"precipitation\": weather.precipitation,\n", "            \"daily_forecasts\": []\n", "        }\n", "\n", "        for daily in weather.daily_forecasts:\n", "            daily_forecast = {\n", "                \"date\": daily.date,\n", "                \"highest_temperature\": daily.highest_temperature,\n", "                \"hourly_forecasts\": daily.hourly_forecasts,\n", "                \"locale\": daily.locale,\n", "                \"lowest_temperature\": daily.lowest_temperature,\n", "                \"moon_illumination\": daily.moon_illumination,\n", "            }\n", "            weather_data[\"daily_forecasts\"].append(daily_forecast)\n", "\n", "        return weather_data\n", "\n", "\n", "# Function to generate weather response using GPT (updated)\n", "async def get_weather_response(location, question):\n", "    weather_data = await get_weather(location)\n", "    \n", "    # Access the necessary values directly from the dictionary\n", "    temperature = weather_data[\"temperature\"]\n", "    description = weather_data[\"description\"]\n", "    forecast_datetime = weather_data[\"datetime\"]\n", "    feels_like = weather_data[\"feels_like\"]\n", "    humidity = weather_data[\"humidity\"]\n", "    wind_speed = weather_data[\"wind_speed\"]\n", "    kind = weather_data[\"kind\"]\n", "    uv = weather_data[\"uv\"]\n", "    precipitation = weather_data[\"precipitation\"]\n", "\n", "    prompt = f\"\"\"\n", "    You are a weather chatbot. Given the following weather information and a question, provide a concise and informative answer.\n", "\n", "    Location: {location}\n", "    Temperature: {temperature}°C\n", "    Description: {description}\n", "    Date: {forecast_datetime}\n", "    Feels Like: {feels_like}°C\n", "    Humidity: {humidity}%\n", "    Wind Speed: {wind_speed} km/h\n", "    Kind: {kind}\n", "    UV Index: {uv}\n", "    Precipitation: {precipitation}\n", "\n", "    Daily Forecasts:\n", "    \"\"\"\n", "    \n", "    for daily in weather_data['daily_forecasts']:\n", "        prompt += f\"\"\"\n", "        Date: {daily['date']}\n", "        Highest Temperature: {daily['highest_temperature']}°C\n", "        Lowest Temperature: {daily['lowest_temperature']}°C\n", "        Moon Illumination: {daily['moon_illumination']}\n", "        \"\"\"\n", "        \n", "        # hourly forecasts\n", "        for hourly in daily['hourly_forecasts']:\n", "            prompt += f\"\"\"\n", "            Time: {hourly.time}, Temperature: {hourly.temperature}°C, Description: {hourly.description}\n", "            \"\"\"\n", "            \n", "    prompt += f\"\"\"       \n", "    Question: {question}\n", "    Answer:\n", "    \"\"\"\n", "\n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-3.5-turbo\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "            {\"role\": \"user\", \"content\": prompt}\n", "        ],\n", "        max_tokens=150,\n", "        n=1,\n", "        stop=None,\n", "        temperature=0.7,\n", "    )\n", "\n", "    return response.choices[0].message.content.strip()  # Updated response extraction\n", "\n", "\n", "# Main function\n", "def main():\n", "    st.set_page_config(page_title=\"Weather Chatbot 🌤️\", layout=\"wide\")  # Set page config for wider layout\n", "\n", "    # Weather Chatbot Header\n", "    st.markdown(\n", "        \"\"\"\n", "        <div id=\"weather-chatbot-header\">\n", "            <h1><PERSON> Chatbot 🌤️</h1>\n", "            <div id=\"location-container\">\n", "                <span id=\"current-location\">Default location: Berlin</span> \n", "                <i class=\"fa fa-info-circle\" aria-hidden=\"true\"></i>\n", "            </div>\n", "        </div>\n", "        \"\"\",\n", "        unsafe_allow_html=True,\n", "    )\n", "    \n", "    # Initialize chat history\n", "    if \"messages\" not in st.session_state:\n", "        st.session_state.messages = []\n", "        \n", "    # Initialize location (default: Hamburg)\n", "    if \"location\" not in st.session_state:\n", "        st.session_state.location = \"Berlin\"\n", "\n", "    # Display chat messages from history\n", "    for message in st.session_state.messages:\n", "        with st.chat_message(message[\"role\"]):\n", "            st.markdown(message[\"content\"])\n", "\n", "    # User input (updated to handle location changes)\n", "    if prompt := st.chat_input(\"Ask me about the weather...\"):\n", "        st.session_state.messages.append({\"role\": \"user\", \"content\": prompt})\n", "        with st.chat_message(\"user\"):\n", "            st.markdown(prompt)\n", "            \n", "        # Location Change Detection (using spaCy for NER)\n", "        doc = nlp(prompt)\n", "        for ent in doc.ents:\n", "            if ent.label_ == \"GPE\":  # GPE = Geopolitical Entity (location)\n", "                new_location = ent.text\n", "                st.session_state.location = new_location\n", "                st.session_state.messages.append({\n", "                    \"role\": \"assistant\",\n", "                    \"content\": f\"Location changed to {new_location}. Here's the weather forecast:\"\n", "                })\n", "                break  # Stop after the first location is found\n", "    \n", "        # Get weather response\n", "        response = asyncio.run(get_weather_response(st.session_state.location, prompt))  # Use updated location\n", "\n", "\n", "        # Append bot response to chat history\n", "        st.session_state.messages.append({\"role\": \"assistant\", \"content\": response})\n", "        with st.chat_message(\"assistant\"):\n", "            st.markdown(response)\n", "\n", "        # Scroll to the bottom of the chat container after new message\n", "        st.markdown(\n", "            \"\"\"\n", "            <script>\n", "                const chat_container = document.querySelector('.css-ytpt3y.e1fqkh3o11');\n", "                if (chat_container) {\n", "                    chat_container.scrollTop = chat_container.scrollHeight;\n", "                }\n", "            </script>\n", "            \"\"\",\n", "            unsafe_allow_html=True,\n", "        )\n", "    \n", "        # CSS for Fixed Header and Location Container\n", "        st.markdown(\n", "            \"\"\"\n", "            <style>\n", "                #weather-chatbot-header {\n", "                    position: initial;\n", "                    top: 0;\n", "                    width: 100%;\n", "                    z-index: 1000;\n", "                    background-color: #222326;\n", "                    padding: 5px;\n", "                }\n", "                #location-container {\n", "                    margin-left: 20px; \n", "                }\n", "                .stApp { \n", "                    margin-top: 0px; \n", "                }\n", "                #current-location {\n", "                  color:white;\n", "                }\n", "                #location-instruction {\n", "                    color:white;\n", "                }\n", "    \n", "                .css-ytpt3y.e1fqkh3o11 {\n", "                    overflow-y: auto;\n", "                    height: 300px; /* or any height that you prefer */\n", "                }\n", "                .st-emotion-cache {\n", "                    width: 100%;\n", "                    inset: 1%;\n", "                    min-width: auto;\n", "                    max-width: initial;\n", "                }\n", "            </style>\n", "            \"\"\",\n", "            unsafe_allow_html=True,\n", "        )\n", "\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "48ebdb5a-8c8d-4085-8754-efa07b764fa2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}