#!/usr/bin/env python3
import os
import subprocess
import sys

def extract_text_from_pdf(pdf_path):
    """
    Extract text from a PDF file using macOS's built-in mdls and textutil
    """
    # Create a temporary HTML file
    temp_html = "temp_output.html"
    
    try:
        # Use textutil to convert PDF to HTML
        subprocess.run(["textutil", "-convert", "html", "-output", temp_html, pdf_path], check=True)
        
        # Read the HTML file
        with open(temp_html, 'r', encoding='utf-8', errors='ignore') as f:
            html_content = f.read()
        
        # Extract text from HTML (very basic extraction)
        # Remove HTML tags
        text = ""
        in_tag = False
        for char in html_content:
            if char == '<':
                in_tag = True
            elif char == '>':
                in_tag = False
            elif not in_tag:
                text += char
        
        return text
    except Exception as e:
        return f"Error extracting text: {str(e)}"
    finally:
        # Clean up temporary file
        if os.path.exists(temp_html):
            os.remove(temp_html)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_pdf_text.py <pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        print(f"Error: File '{pdf_path}' not found.")
        sys.exit(1)
    
    text = extract_text_from_pdf(pdf_path)
    print(text)
