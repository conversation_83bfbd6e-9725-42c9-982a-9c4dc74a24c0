{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}