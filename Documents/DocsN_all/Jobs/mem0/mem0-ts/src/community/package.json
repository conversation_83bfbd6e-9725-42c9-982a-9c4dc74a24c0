{"name": "@mem0/community", "version": "0.0.1", "description": "Community features for Mem0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./langchain": {"types": "./dist/integrations/langchain/index.d.ts", "require": "./dist/integrations/langchain/index.js", "import": "./dist/integrations/langchain/index.mjs"}}, "files": ["dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && npx prettier --check . && npx tsup", "dev": "npx nodemon", "test": "jest", "test:ts": "jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "format": "npm run clean && prettier --write .", "format:check": "npm run clean && prettier --check .", "prepublishOnly": "npm run build"}, "tsup": {"entry": {"index": "src/index.ts", "integrations/langchain/index": "src/integrations/langchain/index.ts"}, "format": ["cjs", "esm"], "dts": {"resolve": true, "compilerOptions": {"rootDir": "src"}}, "splitting": false, "sourcemap": true, "clean": true, "treeshake": true, "minify": false, "outDir": "dist", "tsconfig": "./tsconfig.json"}, "keywords": ["mem0", "community", "ai", "memory"], "author": "<PERSON><PERSON><PERSON>", "license": "Apache-2.0", "devDependencies": {"@types/node": "^22.7.6", "@types/uuid": "^9.0.8", "dotenv": "^16.4.5", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.5.2", "rimraf": "^5.0.5", "ts-jest": "^29.2.6", "tsup": "^8.3.0", "typescript": "5.5.4"}, "dependencies": {"@langchain/community": "^0.3.36", "@langchain/core": "^0.3.42", "axios": "1.7.7", "mem0ai": "^2.1.8", "uuid": "9.0.1", "zod": "3.22.4"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}}