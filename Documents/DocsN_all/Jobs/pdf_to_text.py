#!/usr/bin/env python3
import os
import sys
import subprocess
import tempfile

def extract_text_from_pdf(pdf_path):
    """
    Extract text from a PDF file using macOS's built-in tools
    """
    try:
        # Create a temporary file for the text output
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_txt_path = temp_file.name
        
        # Use macOS's built-in sips to convert PDF to text
        cmd = ['sips', '-s', 'format', 'txt', pdf_path, '--out', temp_txt_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            return f"Error converting PDF: {result.stderr}"
        
        # Read the text file
        with open(temp_txt_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        
        return text
    except Exception as e:
        return f"Error extracting text: {str(e)}"
    finally:
        # Clean up temporary file
        if 'temp_txt_path' in locals() and os.path.exists(temp_txt_path):
            os.remove(temp_txt_path)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python pdf_to_text.py <pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        print(f"Error: File '{pdf_path}' not found.")
        sys.exit(1)
    
    text = extract_text_from_pdf(pdf_path)
    print(text)
