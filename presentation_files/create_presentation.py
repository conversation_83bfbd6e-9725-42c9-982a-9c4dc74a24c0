"""
Create a pitch presentation for the Euranet AI Chat Analytical Platform.
"""

import os
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

# Define colors based on the dashboard's color palette
PRIMARY_COLOR = RGBColor(41, 128, 185)  # Blue
SECONDARY_COLOR = RGBColor(142, 68, 173)  # Purple
ACCENT_COLOR = RGBColor(26, 188, 156)  # Teal
TEXT_COLOR = RGBColor(44, 62, 80)  # Dark gray
LIGHT_TEXT_COLOR = RGBColor(236, 240, 241)  # Light gray

def add_title_slide(prs):
    """Add the title slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[0])  # Title slide layout
    
    # Set background to gradient
    background = slide.background
    fill = background.fill
    fill.solid()
    fill.fore_color.rgb = PRIMARY_COLOR
    
    # Add title
    title = slide.shapes.title
    title.text = "Euranet AI Chat Analytical Platform"
    title.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    title.text_frame.paragraphs[0].font.color.rgb = LIGHT_TEXT_COLOR
    title.text_frame.paragraphs[0].font.size = Pt(44)
    title.text_frame.paragraphs[0].font.bold = True
    
    # Add subtitle
    subtitle = slide.placeholders[1]
    subtitle.text = "Transforming Media Data into Actionable Insights"
    subtitle.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    subtitle.text_frame.paragraphs[0].font.color.rgb = LIGHT_TEXT_COLOR
    subtitle.text_frame.paragraphs[0].font.size = Pt(24)
    
    # Add dashboard icon
    img_path = os.path.join('presentation_files', 'presentation_assets', 'dashboard_icon.png')
    if os.path.exists(img_path):
        left = Inches(4.5)
        top = Inches(2.5)
        width = Inches(2)
        slide.shapes.add_picture(img_path, left, top, width=width)

def add_challenges_slide(prs):
    """Add the challenges slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Current Challenges"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Data Distribution and Analysis Challenges:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "Data distributed across station-wise Google spreadsheets",
        "Multiple sheets for different platforms within each station",
        "Manual consolidation into master spreadsheet required",
        "Platform-specific sheets make cross-platform analysis difficult",
        "Time-consuming process to generate insights from fragmented data"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add fragmented data icon
    img_path = os.path.join('presentation_files', 'presentation_assets', 'fragmented_data_icon.png')
    if os.path.exists(img_path):
        left = Inches(7.5)
        top = Inches(2.5)
        width = Inches(2)
        slide.shapes.add_picture(img_path, left, top, width=width)

def add_solution_slide(prs):
    """Add the solution slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Our Solution"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Centralized AI Chat Analytical Platform:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "Centralized SQLite database with proper schema",
        "AI-powered chat interface for natural language queries",
        "Unified view of data across all media platforms",
        "Interactive visualizations and data tables",
        "Intelligent insights through multi-agent AI analysis"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add chatbot screenshot
    img_path = os.path.join('presentation_files', 'presentation_assets', 'ChatBot_SS.png')
    if os.path.exists(img_path):
        left = Inches(6)
        top = Inches(2)
        height = Inches(3.5)
        slide.shapes.add_picture(img_path, left, top, height=height)

def add_features_slide(prs):
    """Add the key features slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Key Features"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Create a table for features
    left = Inches(0.5)
    top = Inches(1.5)
    width = Inches(9)
    height = Inches(4)
    
    # Create a shape for the table
    table_placeholder = slide.shapes.add_table(5, 2, left, top, width, height).table
    
    # Set column widths
    table_placeholder.columns[0].width = Inches(2)
    table_placeholder.columns[1].width = Inches(7)
    
    # Define features
    features = [
        ("Natural Language Queries", "Ask questions in plain English - no technical knowledge required"),
        ("Unified Interface", "Access data from all media platforms in one place"),
        ("Interactive Visualizations", "Explore data through charts and graphs with hover effects"),
        ("Multi-Agent AI Analysis", "Get intelligent insights from specialized AI agents"),
        ("Flexible Metric Calculations", "Analyze data using various metrics and aggregations")
    ]
    
    # Add features to table
    for i, (feature, description) in enumerate(features):
        # Feature name cell
        cell = table_placeholder.cell(i, 0)
        cell.text = feature
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(16)
        
        # Feature description cell
        cell = table_placeholder.cell(i, 1)
        cell.text = description
        cell.text_frame.paragraphs[0].font.size = Pt(14)
    
    # Add icons for each feature
    icon_files = [
        'data_analytics_icon.png',  # Natural Language Queries
        'unified_dashboard_icon.png',  # Unified Interface
        'metrics_icon.png',  # Interactive Visualizations
        'ai_agents_icon.png',  # Multi-Agent AI Analysis
        'ui_components_icon.png'  # Flexible Metric Calculations
    ]
    
    for i, icon_file in enumerate(icon_files):
        img_path = os.path.join('presentation_files', 'presentation_assets', icon_file)
        if os.path.exists(img_path):
            left = Inches(0.2)
            top = Inches(1.7 + i * 0.8)
            height = Inches(0.6)
            slide.shapes.add_picture(img_path, left, top, height=height)

def add_architecture_slide(prs):
    """Add the technical architecture slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Technical Architecture"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Component-Based Architecture:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "SQLite database with optimized schema",
        "Model-View-Controller (MVC) pattern",
        "Python backend with Dash framework",
        "Natural language processing for query understanding",
        "Multi-agent system for specialized insights"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add database schema screenshot
    img_path = os.path.join('presentation_files', 'presentation_assets', 'DB_Schema_SS.png')
    if os.path.exists(img_path):
        left = Inches(5.5)
        top = Inches(1.8)
        height = Inches(3.5)
        slide.shapes.add_picture(img_path, left, top, height=height)

def add_demo_slide(prs):
    """Add the demo highlights slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Demo Highlights"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Working Prototype Features:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "Natural language chat interface",
        "Interactive data visualizations with hover effects",
        "Detailed data tables for in-depth analysis",
        "Key insights section with AI-generated observations",
        "Dark/light theme toggle for user preference"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add chatbot screenshot
    img_path = os.path.join('presentation_files', 'presentation_assets', 'ChatBot_SS.png')
    if os.path.exists(img_path):
        left = Inches(5.5)
        top = Inches(1.8)
        height = Inches(3.5)
        slide.shapes.add_picture(img_path, left, top, height=height)

def add_benefits_slide(prs):
    """Add the benefits slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Benefits"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Create two text boxes for different user groups
    
    # Management benefits
    left = Inches(0.5)
    top = Inches(1.5)
    width = Inches(4.5)
    height = Inches(4)
    txBox = slide.shapes.add_textbox(left, top, width, height)
    tf = txBox.text_frame
    
    p = tf.add_paragraph()
    p.text = "For Management:"
    p.font.bold = True
    p.font.size = Pt(20)
    p.font.color.rgb = SECONDARY_COLOR
    
    management_benefits = [
        "Comprehensive view across all media platforms",
        "Faster access to critical performance metrics",
        "Data-driven decision making",
        "Reduced time spent on manual data consolidation",
        "Easier identification of trends and opportunities"
    ]
    
    for benefit in management_benefits:
        p = tf.add_paragraph()
        p.text = "• " + benefit
        p.font.size = Pt(16)
    
    # Content creator benefits
    left = Inches(5.5)
    top = Inches(1.5)
    width = Inches(4.5)
    height = Inches(4)
    txBox = slide.shapes.add_textbox(left, top, width, height)
    tf = txBox.text_frame
    
    p = tf.add_paragraph()
    p.text = "For Content Creators:"
    p.font.bold = True
    p.font.size = Pt(20)
    p.font.color.rgb = ACCENT_COLOR
    
    creator_benefits = [
        "Immediate feedback on content performance",
        "Insights into audience preferences",
        "Platform-specific performance analysis",
        "Content optimization recommendations",
        "Simplified reporting process"
    ]
    
    for benefit in creator_benefits:
        p = tf.add_paragraph()
        p.text = "• " + benefit
        p.font.size = Pt(16)
    
    # Add benefits icon
    img_path = os.path.join('presentation_files', 'presentation_assets', 'benefits_icon.png')
    if os.path.exists(img_path):
        left = Inches(4.5)
        top = Inches(4.5)
        width = Inches(1)
        slide.shapes.add_picture(img_path, left, top, width=width)

def add_status_slide(prs):
    """Add the current status slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Current Status"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Project Progress:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "Centralized SQLite database created with proper schema",
        "Prototype of AI chat analytical platform developed",
        "Functional demo presented to Mareike",
        "Basic LLM model integrated for natural language understanding",
        "Interactive visualizations and data tables implemented"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add status visualization
    left = Inches(1)
    top = Inches(4)
    width = Inches(8)
    height = Inches(1)
    shape = slide.shapes.add_shape(1, left, top, width, height)  # Progress bar shape
    
    # Set progress bar fill
    fill = shape.fill
    fill.solid()
    fill.fore_color.rgb = ACCENT_COLOR
    
    # Add text to progress bar
    tf = shape.text_frame
    p = tf.add_paragraph()
    p.text = "Prototype Phase: 75% Complete"
    p.alignment = PP_ALIGN.CENTER
    p.font.color.rgb = LIGHT_TEXT_COLOR
    p.font.size = Pt(16)
    p.font.bold = True

def add_roadmap_slide(prs):
    """Add the roadmap slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Roadmap"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Add content
    content = slide.placeholders[1]
    tf = content.text_frame
    
    p = tf.paragraphs[0]
    p.text = "Future Enhancements:"
    p.font.bold = True
    p.font.size = Pt(20)
    
    bullet_points = [
        "Component-based architecture following MVC/MVP patterns",
        "Local LLM integration with Gemma 3-1b-it model",
        "Multi-agent system for specialized insight generation",
        "Flexible metric calculation engine",
        "Automated station file upload system",
        "LLM-powered data validation and schema mapping"
    ]
    
    for point in bullet_points:
        p = tf.add_paragraph()
        p.text = point
        p.level = 1
        p.font.size = Pt(18)
    
    # Add roadmap icon
    img_path = os.path.join('presentation_files', 'presentation_assets', 'roadmap_icon.png')
    if os.path.exists(img_path):
        left = Inches(7.5)
        top = Inches(2.5)
        width = Inches(2)
        slide.shapes.add_picture(img_path, left, top, width=width)

def add_next_steps_slide(prs):
    """Add the next steps slide to the presentation."""
    slide = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    
    # Add title
    title = slide.shapes.title
    title.text = "Next Steps"
    title.text_frame.paragraphs[0].font.color.rgb = TEXT_COLOR
    
    # Create a timeline
    timeline_items = [
        ("Phase 1", "Finalize prototype and gather feedback"),
        ("Phase 2", "Implement MVC architecture and UI enhancements"),
        ("Phase 3", "Integrate Gemma 3-1b-it model for improved NLP"),
        ("Phase 4", "Develop automated file upload and validation system"),
        ("Phase 5", "Full deployment and user training")
    ]
    
    # Draw timeline
    timeline_top = Inches(1.8)
    timeline_left = Inches(0.5)
    timeline_width = Inches(9)
    timeline_height = Inches(0.1)
    
    # Timeline line
    line = slide.shapes.add_shape(1, timeline_left, timeline_top, timeline_width, timeline_height)
    line.fill.solid()
    line.fill.fore_color.rgb = PRIMARY_COLOR
    line.line.fill.solid()
    line.line.fill.fore_color.rgb = PRIMARY_COLOR
    
    # Add timeline items
    segment_width = timeline_width.inches / (len(timeline_items))
    
    for i, (phase, description) in enumerate(timeline_items):
        # Add circle marker
        marker_left = timeline_left.inches + (i * segment_width) + (segment_width / 2) - 0.15
        marker_top = timeline_top.inches - 0.15
        marker_size = Inches(0.3)
        
        marker = slide.shapes.add_shape(3, Inches(marker_left), Inches(marker_top), marker_size, marker_size)
        marker.fill.solid()
        marker.fill.fore_color.rgb = SECONDARY_COLOR
        
        # Add phase text above
        text_left = timeline_left.inches + (i * segment_width)
        text_top = timeline_top.inches - 0.8
        text_width = Inches(segment_width)
        text_height = Inches(0.5)
        
        txt_box = slide.shapes.add_textbox(Inches(text_left), Inches(text_top), text_width, text_height)
        tf = txt_box.text_frame
        p = tf.add_paragraph()
        p.text = phase
        p.alignment = PP_ALIGN.CENTER
        p.font.bold = True
        p.font.size = Pt(14)
        p.font.color.rgb = SECONDARY_COLOR
        
        # Add description text below
        desc_left = timeline_left.inches + (i * segment_width)
        desc_top = timeline_top.inches + 0.3
        desc_width = Inches(segment_width)
        desc_height = Inches(1)
        
        desc_box = slide.shapes.add_textbox(Inches(desc_left), Inches(desc_top), desc_width, desc_height)
        tf = desc_box.text_frame
        p = tf.add_paragraph()
        p.text = description
        p.alignment = PP_ALIGN.CENTER
        p.font.size = Pt(12)
    
    # Add implementation requirements section
    req_left = Inches(0.5)
    req_top = Inches(3.5)
    req_width = Inches(9)
    req_height = Inches(2)
    
    req_box = slide.shapes.add_textbox(req_left, req_top, req_width, req_height)
    tf = req_box.text_frame
    
    p = tf.add_paragraph()
    p.text = "Implementation Requirements:"
    p.font.bold = True
    p.font.size = Pt(18)
    
    requirements = [
        "Development resources for UI and backend enhancements",
        "Server infrastructure for hosting the application",
        "Training data for the Gemma 3-1b-it model",
        "User feedback sessions for iterative improvements",
        "Documentation and training materials for end users"
    ]
    
    for req in requirements:
        p = tf.add_paragraph()
        p.text = "• " + req
        p.font.size = Pt(14)
    
    # Add next steps icon
    img_path = os.path.join('presentation_files', 'presentation_assets', 'next_steps_icon.png')
    if os.path.exists(img_path):
        left = Inches(8)
        top = Inches(4.5)
        width = Inches(1.5)
        slide.shapes.add_picture(img_path, left, top, width=width)

def create_presentation():
    """Create the complete presentation."""
    prs = Presentation()
    
    # Set slide dimensions to 16:9 aspect ratio
    prs.slide_width = Inches(10)
    prs.slide_height = Inches(5.625)
    
    # Add slides
    add_title_slide(prs)
    add_challenges_slide(prs)
    add_solution_slide(prs)
    add_features_slide(prs)
    add_architecture_slide(prs)
    add_demo_slide(prs)
    add_benefits_slide(prs)
    add_status_slide(prs)
    add_roadmap_slide(prs)
    add_next_steps_slide(prs)
    
    # Save the presentation
    output_path = os.path.join('presentation_files', 'Euranet_AI_Platform_Pitch.pptx')
    prs.save(output_path)
    print(f"Presentation saved to {output_path}")

if __name__ == "__main__":
    create_presentation()
