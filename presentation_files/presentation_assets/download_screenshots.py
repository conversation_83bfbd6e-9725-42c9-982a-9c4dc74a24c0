#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to download and save the screenshots for the Euranet presentation
"""

import os
import requests
from PIL import Image
from io import BytesIO

# URLs of the screenshots (replace with actual URLs)
CHATBOT_SCREENSHOT_URL = "https://raw.githubusercontent.com/user/repo/main/euranet_chatbot_interface.png"
DATABASE_SCREENSHOT_URL = "https://raw.githubusercontent.com/user/repo/main/euranet_database_schema.png"

# Local paths to save the screenshots
CHATBOT_SCREENSHOT_PATH = "euranet_chatbot_interface.png"
DATABASE_SCREENSHOT_PATH = "euranet_database_schema.png"

def download_image(url, path):
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        img = Image.open(BytesIO(response.content))
        img.save(path)
        print(f"Downloaded and saved {path}")
        return True
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return False

# Download the screenshots
download_image(CHATBOT_SCREENSHOT_URL, CHATBOT_SCREENSHOT_PATH)
download_image(DATABASE_SCREENSHOT_URL, DATABASE_SCREENSHOT_PATH)

print("Done downloading screenshots.")
