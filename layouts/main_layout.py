"""
Main layout for the Euranet Dashboard application.
This file defines the overall layout structure of the application.
"""

import dash
from dash import html, dcc
import dash_bootstrap_components as dbc

from constants import *
from layouts.chat_panel import create_chat_panel
from layouts.visualization_panel import create_visualization_panel

def create_layout(metric_selector=None):
    """
    Create the main layout for the application.

    Returns:
        dbc.Container: The main container with the application layout.
    """
    layout = dbc.Container(
        id=APP_CONTAINER_ID,
        fluid=True,
        className="app-container dark-mode",
        children=[
            # Store components for state management
            dcc.Store(id=THEME_STORE_ID, data={'dark_mode': True}),  # Default to dark mode
            dcc.Store(id=CHAT_HISTORY_STORE_ID, data={
                'messages': [{
                    "role": "assistant",
                    "content": "Welcome to the Euranet Database Chatbot! Ask me anything about podcast and social media data.",
                    "timestamp": 0
                }]
            }),
            dcc.Store(id=CURRENT_RESULT_STORE_ID, data=None),
            dcc.Store(id=LOADING_STATE_STORE_ID, data={'is_loading': False}),  # Store for loading state
            dcc.Store(id=KEYPRESS_STORE_ID, data=None),  # Store for keypress events

            # Header Row
            create_header(),

            # Main Content Area (Split Panel)
            dbc.Row(className="main-content", children=[
                # Left Panel - Chat
                create_chat_panel(),

                # Right Panel - Data & Insights
                create_visualization_panel(metric_selector=metric_selector)
            ])
        ]
    )

    return layout

def create_header():
    """
    Create the header section of the application.

    Returns:
        dbc.Row: The header row with left and right sections.
    """
    return dbc.Row(className="header", align="center", children=[
        # Left Header Section (aligns with Chat Panel)
        dbc.Col(width=6, children=[
            html.Div([
                html.Div(className="header-icon-container", children=html.I(className="fas fa-comments header-icon")),
                html.Div([
                    html.H1("Euranet Chatbot", className="header-title"),
                    html.P("Ask questions about podcast and social media data", className="header-subtitle")
                ])
            ], style={'display': 'flex', 'alignItems': 'center'})
        ]),
        # Right Header Section (aligns with Visualization Panel)
        dbc.Col(width=6, children=[
            html.Div([  # Wrap insights title and theme toggle
                # Group Title and Subtitle
                html.Div([
                    html.H2("Data Insights", className="header-title"),
                    html.P("Visualizations and analysis from your queries", className="header-subtitle")
                ]),
                # Theme Toggle Button - pushed to the far right
                dbc.Button(
                    children=[html.I(id=THEME_ICON_ID, className="fas fa-moon")],  # Start with moon icon for dark mode
                    id=THEME_TOGGLE_BUTTON_ID,
                    color="secondary",
                    outline=True,
                    className="theme-toggle-button ms-auto"  # Use ms-auto to push right
                )
            ], className="d-flex align-items-center w-100")  # Flex container for right side
        ]),
    ])
