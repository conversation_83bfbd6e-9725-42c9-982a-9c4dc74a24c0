"""
Visualization panel layout for the Euranet Dashboard application.
This file defines the layout for the data visualization interface.
"""

from dash import html, dcc
import dash_bootstrap_components as dbc

from constants import *

def create_visualization_panel(metric_selector=None):
    """
    Create the visualization panel layout.

    Returns:
        dbc.Col: The visualization panel column with all its components.
    """
    return dbc.Col(className="visualization-panel", width=6, children=[
        # Main content area (scrollable)
        html.Div(id=VISUALIZATION_CONTENT_AREA_ID, className="visualization-container", children=[
            # Content (Welcome message or results) will be rendered here by callback
            create_empty_state()
        ]),

        # Fixed Key Insights section at the bottom
        create_key_insights_section(metric_selector=metric_selector)
    ])

def create_empty_state():
    """
    Create the empty state display for when no data is available.

    Returns:
        html.Div: The empty state container.
    """
    return html.Div(className="empty-state-container", children=[
        html.Div("📊", style={"fontSize": "3rem", "marginBottom": "1rem"}),
        html.H3("Ask a question to see insights", className="subtitle"),
        html.P("Your query results and visualizations will appear here", style={"opacity": 0.7})
    ])

def create_key_insights_section(metric_selector=None):
    """
    Create the fixed key insights section at the bottom of the visualization panel.
    Enhanced with ADK to provide better visual separation and styling.

    Returns:
        html.Div: The key insights section container.
    """
    return html.Div(id=KEY_INSIGHTS_SECTION_ID, className="key-insights-fixed-section", children=[
        # Enhanced visual separator with gradient
        html.Div(className="key-insights-visual-separator"),

        # Title bar with icon
        html.Div(className="key-insights-title-bar", children=[
            html.Div(className="key-insights-title", children=[
                html.I(className="fas fa-lightbulb", style={"marginRight": "0.5rem"}),
                "Key Insights"
            ]),
            # Optional: Add a settings button for metric selection
            html.Div(className="key-insights-settings", children=[
                html.Div(id=KEY_INSIGHTS_METRIC_SELECTOR_ID, className="metric-selector", children=[
                    metric_selector.layout() if metric_selector else html.Div()
                ])
            ])
        ]),

        # Content area with scrollable design
        html.Div(id=KEY_INSIGHTS_CONTENT_ID, className="key-insights-content")
    ])
