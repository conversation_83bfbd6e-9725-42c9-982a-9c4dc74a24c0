#!/bin/bash

# Create a timestamped backup directory
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="../euranet_backup_${TIMESTAMP}"

echo "Creating backup directory: ${BACKUP_DIR}"
mkdir -p "${BACKUP_DIR}"

# Copy euranet_dash_fresh to backup directory, excluding unnecessary files
echo "Backing up euranet_dash_fresh..."
rsync -av --progress ./euranet_dash_fresh/ "${BACKUP_DIR}/euranet_dash_fresh/" \
    --exclude="venv_dash" \
    --exclude=".git" \
    --exclude="__pycache__" \
    --exclude="*.pyc" \
    --exclude=".DS_Store"

# If euranet_dash exists, also back it up
if [ -d "./euranet_dash" ]; then
    echo "Backing up euranet_dash..."
    rsync -av --progress ./euranet_dash/ "${BACKUP_DIR}/euranet_dash/" \
        --exclude="venv_dash" \
        --exclude=".git" \
        --exclude="__pycache__" \
        --exclude="*.pyc" \
        --exclude=".DS_Store"
fi

echo "Backup completed to ${BACKUP_DIR}"

# Create a new Git branch for Gemma integration
cd euranet_dash_fresh
git checkout -b feature/gemma_integration

echo "Created new Git branch: feature/gemma_integration"
echo "You can now proceed with Gemma integration"
