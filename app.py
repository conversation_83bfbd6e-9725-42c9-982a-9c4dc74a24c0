"""Euranet Dashboard Application
A Dash application for querying and visualizing Euranet podcast and social media data.
"""

import os
import sys
import dash
import dash_bootstrap_components as dbc

# Add the components directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))

# Import backend components
from components.database import DatabaseConnector
from components.metric_calculator import MetricCalculator
from components.metric_selector import MetricSelector

# Import layouts and callbacks
from layouts.main_layout import create_layout
from callbacks.chat_callbacks import register_chat_callbacks
from callbacks.visualization_callbacks import register_visualization_callbacks
from callbacks.theme_callbacks import register_theme_callbacks

# --- Initialize Database Connection ---
db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                       'EuranetDB', 'euranet_data.db')
if not os.path.exists(db_path):
    db_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/Other computers/Windows/Work/Mareike-Brussel/EuranetAutomation/Final Format 2023/EuranetDB/euranet_data.db"

db = DatabaseConnector(db_path)

# --- Initialize Dash App ---
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME],
    suppress_callback_exceptions=True
)
server = app.server  # Expose server for deployment

# Initialize metric calculator and selector
metric_calculator = MetricCalculator()
metric_selector = MetricSelector(app)

# Set the app layout
app.layout = create_layout(metric_selector=metric_selector)

# Register callbacks
register_chat_callbacks(db)
register_visualization_callbacks()
register_theme_callbacks()

# --- Run the App ---
if __name__ == '__main__':
    # Add Font Awesome CSS
    app.index_string = '''
    <!DOCTYPE html>
    <html>
        <head>
            {%metas%}
            <title>{%title%}</title>
            {%favicon%}
            {%css%}
            <!-- Font Awesome is now included via external_stylesheets -->
            <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> -->
            <!-- styles.css is automatically loaded by Dash from the assets folder -->
            <!-- <link rel="stylesheet" href="/assets/styles.css"> -->
        </head>
        <body>
            {%app_entry%}
            <footer>
                {%config%}
                {%scripts%}
                {%renderer%}
            </footer>
        </body>
    </html>
    '''
    app.run_server(debug=True, port=8054) # Use debug=True for development
