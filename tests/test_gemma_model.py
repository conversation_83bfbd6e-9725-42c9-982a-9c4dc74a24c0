"""
Test for the GemmaModel component.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from components.gemma_model import GemmaModel

def test_gemma_model():
    """Test the GemmaModel component."""
    print("=== Testing Gemma Model ===\n")
    
    # Initialize the component
    gemma_model = GemmaModel()
    print("Gemma Model initialized successfully")
    
    # Test setting database schema
    print("\n1. Testing set_db_schema:")
    db_schema = {
        "tables": ["podcast_entries", "social_media_entries"],
        "columns": {
            "podcast_entries": ["id", "country_id", "station_id", "platform_id", "date", "plays", "listeners"],
            "social_media_entries": ["id", "country_id", "station_id", "platform_id", "date", "reach", "impressions", "engagements"]
        }
    }
    gemma_model.set_db_schema(db_schema)
    print("  Database schema set successfully")
    
    # Test setting metrics information
    print("\n2. Testing set_metrics_info:")
    metrics_info = {
        "platforms": {
            "podcast": ["Spotify", "ApplePodcast"],
            "social": ["Facebook", "Instagram"],
            "web": ["URL"]
        },
        "metrics": {
            "Spotify": {
                "primary": ["plays", "listeners"],
                "secondary": ["starts", "streams"],
                "calculated": ["completion_rate"]
            },
            "Facebook": {
                "primary": ["reach", "impressions", "engagements"],
                "secondary": ["likes", "comments", "shares"],
                "calculated": ["engagement_rate"]
            }
        },
        "podcast_types": ["Gen Z", "Green Deal"]
    }
    gemma_model.set_metrics_info(metrics_info)
    print("  Metrics information set successfully")
    
    # Test cache key generation
    print("\n3. Testing _get_cache_key:")
    prompt = "This is a test prompt"
    max_tokens = 256
    cache_key = gemma_model._get_cache_key(prompt, max_tokens)
    print(f"  Cache key: {cache_key}")
    
    # Test generate_response with caching
    print("\n4. Testing generate_response with caching:")
    # First call should not be cached
    response1 = gemma_model.generate_response("What are the top podcasts?", 100)
    print(f"  First response: {response1[:50]}...")
    
    # Second call with same prompt should be cached
    response2 = gemma_model.generate_response("What are the top podcasts?", 100)
    print(f"  Second response (should be cached): {response2[:50]}...")
    
    # Test enhance_query_understanding
    print("\n5. Testing enhance_query_understanding:")
    query = "Show me the top 5 podcasts by plays"
    understanding = gemma_model.enhance_query_understanding(query)
    print(f"  Enhanced understanding: {understanding}")
    
    # Test generate_insights
    print("\n6. Testing generate_insights:")
    # Create a test DataFrame
    data = {
        'station': ['Station A', 'Station B', 'Station C', 'Station D'],
        'country': ['Germany', 'France', 'Italy', 'Spain'],
        'plays': [1000, 2000, 1500, 3000],
        'listeners': [500, 1000, 750, 1500]
    }
    df = pd.DataFrame(data)
    
    insights = gemma_model.generate_insights("Show me the top podcasts by plays", df)
    print("  Generated insights:")
    for i, insight in enumerate(insights):
        print(f"    {i+1}. {insight}")
    
    print("\nTests completed.")

if __name__ == "__main__":
    test_gemma_model()
