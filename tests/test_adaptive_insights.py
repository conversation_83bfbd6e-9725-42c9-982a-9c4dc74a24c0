"""
Test script for Adaptive Insights in the Euranet Dashboard.
"""

import sys
import os

# Add the parent directory to the path to import components
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.insight_analyzer import InsightAnalyzer
from components.adaptive_insights import AdaptiveInsights

def test_insight_analyzer():
    """Test the insight analyzer functionality."""
    analyzer = InsightAnalyzer()
    
    # Test insights with different complexities and categories
    insights = [
        "Germany has the highest average plays with 5,432 plays.",
        "France shows a 15% increase in podcast plays compared to last month.",
        "The overall trend for podcast plays is increasing, with a 23% growth over the past 6 months across all countries.",
        "Instagram has more engagement than Facebook, with 3,210 vs 2,150 average interactions.",
        "Spain has the lowest podcast plays at only 1,200."
    ]
    
    # Analyze insights
    analyzed_insights = analyzer.analyze_insights(insights, query="Show me podcast plays by country")
    
    # Print analysis results
    print("=== Insight Analysis Results ===")
    for i, insight in enumerate(analyzed_insights):
        print(f"Insight {i+1}: {insight['text']}")
        print(f"  Complexity: {insight['complexity']}")
        print(f"  Category: {insight['category']}")
        print(f"  Importance: {insight['importance']}")
        print(f"  Values: {insight['values']}")
        print()

def test_adaptive_layouts():
    """Test the adaptive layouts functionality."""
    adaptive_insights = AdaptiveInsights(dark_mode=True)
    
    # Test with few insights
    few_insights = [
        "Germany has the highest average plays with 5,432 plays.",
        "France shows a 15% increase in podcast plays compared to last month."
    ]
    
    # Test with many insights of different categories
    many_insights = [
        "Germany has the highest average plays with 5,432 plays.",
        "France shows a 15% increase in podcast plays compared to last month.",
        "The overall trend for podcast plays is increasing, with a 23% growth over the past 6 months across all countries.",
        "Instagram has more engagement than Facebook, with 3,210 vs 2,150 average interactions.",
        "Spain has the lowest podcast plays at only 1,200.",
        "The average podcast plays across all countries is 2,845.",
        "Italy shows the most consistent performance with only 5% variation month-to-month."
    ]
    
    # Create layouts
    simple_layout = adaptive_insights.create_adaptive_insights(few_insights)
    complex_layout = adaptive_insights.create_adaptive_insights(many_insights)
    
    # Print layout types
    print("=== Adaptive Layout Results ===")
    print(f"Simple Layout Type: {type(simple_layout)}")
    print(f"Complex Layout Type: {type(complex_layout)}")
    print("Layout creation successful!")

def main():
    """Run all tests."""
    print("=== Testing Adaptive Insights ===")
    print("\n1. Testing Insight Analyzer:")
    test_insight_analyzer()
    
    print("\n2. Testing Adaptive Layouts:")
    test_adaptive_layouts()
    
    print("\nTests completed.")

if __name__ == "__main__":
    main()
