"""
Test script for ADK integration in the Euranet Dashboard.
"""

import sys
import os
import pandas as pd

# Add the parent directory to the path to import components
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.adk_integration import ADKIntegration

def test_metric_detection():
    """Test the metric type detection functionality."""
    adk = ADKIntegration()
    
    # Test queries with different metric types
    queries = [
        ("Show me the average plays by country", "AVG"),
        ("What is the total number of plays?", "SUM"),
        ("Count the number of podcast episodes", "COUNT"),
        ("What percentage of plays comes from Germany?", "PERCENTAGE"),
        ("Show me podcast plays", "AVG")  # Default
    ]
    
    for query, expected_metric in queries:
        detected_metric = adk.detect_metric_type(query)
        print(f"Query: '{query}'")
        print(f"Expected: {expected_metric}, Detected: {detected_metric}")
        print(f"Result: {'✓' if detected_metric == expected_metric else '✗'}")
        print()

def test_metric_processing():
    """Test the metric processing functionality."""
    adk = ADKIntegration()
    
    # Create a test DataFrame
    data = {
        'country': ['Germany', 'France', 'Spain', 'Italy', 'UK'],
        'avg_plays': [100, 200, 150, 300, 250]
    }
    df = pd.DataFrame(data)
    
    # Test different metric types
    metric_types = ["AVG", "SUM", "COUNT", "PERCENTAGE"]
    
    for metric_type in metric_types:
        processed_df = adk.process_metrics(df, metric_type)
        print(f"Metric Type: {metric_type}")
        print(processed_df)
        print()

def main():
    """Run all tests."""
    print("=== Testing ADK Integration ===")
    print("\n1. Testing Metric Detection:")
    test_metric_detection()
    
    print("\n2. Testing Metric Processing:")
    test_metric_processing()
    
    print("\nTests completed.")

if __name__ == "__main__":
    main()
