"""
Test for the MetricCalculator component.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from components.metric_calculator import MetricCalculator

def test_metric_calculator():
    """Test the MetricCalculator component."""
    print("=== Testing Metric Calculator ===\n")
    
    # Initialize the component
    metric_calculator = MetricCalculator()
    print("Metric Calculator initialized successfully")
    
    # Test getting metric types
    print("\n1. Testing get_metric_types:")
    metric_types = metric_calculator.get_metric_types()
    print(f"  Metric types: {metric_types}")
    
    # Test getting default metric type
    print("\n2. Testing get_default_metric_type:")
    default_type = metric_calculator.get_default_metric_type()
    print(f"  Default metric type: {default_type}")
    
    # Test getting display name
    print("\n3. Testing get_display_name:")
    for metric_type in metric_types:
        display_name = metric_calculator.get_display_name(metric_type)
        print(f"  {metric_type}: {display_name}")
    
    # Test getting description
    print("\n4. Testing get_description:")
    for metric_type in metric_types:
        description = metric_calculator.get_description(metric_type)
        print(f"  {metric_type}: {description}")
    
    # Test getting column prefix
    print("\n5. Testing get_column_prefix:")
    for metric_type in metric_types:
        prefix = metric_calculator.get_column_prefix(metric_type)
        print(f"  {metric_type}: {prefix}")
    
    # Test getting SQL aggregation
    print("\n6. Testing get_sql_aggregation:")
    for metric_type in metric_types:
        aggregation = metric_calculator.get_sql_aggregation(metric_type)
        print(f"  {metric_type}: {aggregation}")
    
    # Test calculating metrics
    print("\n7. Testing calculate_metric:")
    # Create a test DataFrame
    data = pd.DataFrame({
        'station': ['Station A', 'Station B', 'Station C', 'Station D'],
        'plays': [100, 200, 300, 400],
        'avg_reach': [1000, 2000, 3000, 4000]
    })
    print("  Original data:")
    print(data)
    
    # Test AVG calculation
    avg_data = metric_calculator.calculate_metric(data, 'plays', 'AVG')
    print("\n  After AVG calculation:")
    print(avg_data)
    
    # Test SUM calculation
    sum_data = metric_calculator.calculate_metric(data, 'plays', 'SUM')
    print("\n  After SUM calculation:")
    print(sum_data)
    
    # Test COUNT calculation
    count_data = metric_calculator.calculate_metric(data, 'plays', 'COUNT')
    print("\n  After COUNT calculation:")
    print(count_data)
    
    # Test PERCENTAGE calculation
    pct_data = metric_calculator.calculate_metric(data, 'plays', 'PERCENTAGE')
    print("\n  After PERCENTAGE calculation:")
    print(pct_data)
    
    # Test updating SQL query
    print("\n8. Testing update_sql_query:")
    test_queries = [
        "SELECT AVG(plays) as avg_plays FROM podcast_entries",
        "SELECT station, AVG(reach) as avg_reach FROM social_media_entries GROUP BY station"
    ]
    
    for query in test_queries:
        print(f"\n  Original query: {query}")
        for metric_type in metric_types:
            updated_query = metric_calculator.update_sql_query(query, 'plays', metric_type)
            print(f"  {metric_type}: {updated_query}")
    
    # Test detecting metric type from query
    print("\n9. Testing detect_metric_type_from_query:")
    test_queries = [
        "Show me the average plays for podcasts",
        "What is the total reach for Facebook?",
        "Count how many podcast episodes each station has",
        "What percentage of plays does each station have?",
        "Show me the top stations by plays"
    ]
    
    for query in test_queries:
        detected_type = metric_calculator.detect_metric_type_from_query(query)
        print(f"  '{query}' -> {detected_type}")
    
    print("\nTests completed.")

if __name__ == "__main__":
    test_metric_calculator()
