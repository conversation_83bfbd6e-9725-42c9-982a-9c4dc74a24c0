"""
Test for the InsightAgents component.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from components.insight_agents import TrendAgent, ComparisonAgent, AnomalyAgent, MultiAgentSystem

def create_test_data():
    """Create test data for insight agents."""
    # Create date range
    dates = [datetime.now() - timedelta(days=i) for i in range(30)]
    dates.reverse()  # Oldest to newest
    
    # Create test data with trends, comparisons, and anomalies
    data = {
        'date': dates,
        'platform': ['Facebook', 'Instagram', 'Twitter', 'LinkedIn'] * 7 + ['Facebook', 'Instagram'],
        'country': ['Germany', 'France', 'Italy', 'Spain'] * 7 + ['Germany', 'France'],
        'views': [100, 150, 120, 130, 110, 160, 125, 140, 
                 120, 170, 130, 150, 130, 180, 140, 160,
                 140, 190, 150, 170, 150, 200, 160, 180,
                 160, 210, 170, 190, 500, 220],  # Anomaly in index 28
        'engagement': [20, 30, 25, 28, 22, 32, 26, 29,
                      24, 34, 27, 30, 26, 36, 28, 31,
                      28, 38, 29, 32, 30, 40, 31, 33,
                      32, 42, 33, 34, 34, 44]
    }
    
    return pd.DataFrame(data)

def test_trend_agent():
    """Test the TrendAgent."""
    print("=== Testing TrendAgent ===\n")
    
    # Create test data
    df = create_test_data()
    
    # Initialize agent
    agent = TrendAgent()
    print("TrendAgent initialized")
    
    # Test insight generation
    query = "Show me the trend of views over time"
    insights = agent.generate_insights(query, df)
    
    print("Generated insights:")
    for i, insight in enumerate(insights):
        print(f"  {i+1}. {insight}")
        
    # Test memory
    print("\nTesting memory:")
    agent.add_to_memory({
        'type': 'trend',
        'content': 'Views have been consistently increasing over the past month.',
        'metric': 'views'
    })
    
    memories = agent.get_relevant_memories("What's happening with views?")
    print(f"  Retrieved {len(memories)} relevant memories")
    for memory in memories:
        print(f"  - {memory['content']}")
        
    print("\nTrendAgent test completed.")

def test_comparison_agent():
    """Test the ComparisonAgent."""
    print("\n=== Testing ComparisonAgent ===\n")
    
    # Create test data
    df = create_test_data()
    
    # Initialize agent
    agent = ComparisonAgent()
    print("ComparisonAgent initialized")
    
    # Test insight generation
    query = "Compare engagement across different platforms"
    insights = agent.generate_insights(query, df)
    
    print("Generated insights:")
    for i, insight in enumerate(insights):
        print(f"  {i+1}. {insight}")
        
    # Test memory
    print("\nTesting memory:")
    agent.add_to_memory({
        'type': 'comparison',
        'content': 'Instagram has 50% higher engagement compared to Twitter.',
        'category': 'platform',
        'metric': 'engagement',
        'entities': ['Instagram', 'Twitter']
    })
    
    memories = agent.get_relevant_memories("How does Instagram compare to other platforms?")
    print(f"  Retrieved {len(memories)} relevant memories")
    for memory in memories:
        print(f"  - {memory['content']}")
        
    print("\nComparisonAgent test completed.")

def test_anomaly_agent():
    """Test the AnomalyAgent."""
    print("\n=== Testing AnomalyAgent ===\n")
    
    # Create test data
    df = create_test_data()
    
    # Initialize agent
    agent = AnomalyAgent()
    print("AnomalyAgent initialized")
    
    # Test insight generation
    query = "Find any unusual patterns in the data"
    insights = agent.generate_insights(query, df)
    
    print("Generated insights:")
    for i, insight in enumerate(insights):
        print(f"  {i+1}. {insight}")
        
    # Test memory
    print("\nTesting memory:")
    agent.add_to_memory({
        'type': 'anomaly',
        'content': 'Detected an unusually high views value (500) for platform: Facebook, 3.5 standard deviations from the mean.',
        'metric': 'views',
        'z_score': 3.5,
        'identifier': 'platform: Facebook'
    })
    
    memories = agent.get_relevant_memories("Are there any anomalies in Facebook data?")
    print(f"  Retrieved {len(memories)} relevant memories")
    for memory in memories:
        print(f"  - {memory['content']}")
        
    print("\nAnomalyAgent test completed.")

def test_multi_agent_system():
    """Test the MultiAgentSystem."""
    print("\n=== Testing MultiAgentSystem ===\n")
    
    # Create test data
    df = create_test_data()
    
    # Initialize system
    system = MultiAgentSystem()
    print("MultiAgentSystem initialized")
    
    # Test insight generation
    query = "Analyze the performance data across platforms"
    insights = system.generate_insights(query, df)
    
    print("Generated insights:")
    for i, insight in enumerate(insights):
        print(f"  {i+1}. {insight}")
        
    # Test agent selection
    print("\nTesting agent selection:")
    trend_query = "Show me the trend of views over time"
    trend_agents = system._select_agents(trend_query, {})
    print(f"  Agents selected for trend query: {trend_agents}")
    
    comparison_query = "Compare engagement between Facebook and Instagram"
    comparison_agents = system._select_agents(comparison_query, {})
    print(f"  Agents selected for comparison query: {comparison_agents}")
    
    anomaly_query = "Find any outliers in the data"
    anomaly_agents = system._select_agents(anomaly_query, {})
    print(f"  Agents selected for anomaly query: {anomaly_agents}")
    
    print("\nMultiAgentSystem test completed.")

if __name__ == "__main__":
    test_trend_agent()
    test_comparison_agent()
    test_anomaly_agent()
    test_multi_agent_system()
