"""
Test for the PlatformMetrics component.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.platform_metrics import PlatformMetrics

def test_platform_metrics():
    """Test the PlatformMetrics component."""
    print("=== Testing Platform Metrics ===\n")

    # Initialize the component
    platform_metrics = PlatformMetrics()
    print("Platform Metrics initialized successfully")

    # Test getting platform category
    print("\n1. Testing get_platform_category:")
    platforms = ["Spotify", "Facebook", "Website", "Unknown"]
    for platform in platforms:
        category = platform_metrics.get_platform_category(platform)
        print(f"  {platform}: {category}")

    # Test getting metrics for platform
    print("\n2. Testing get_metrics_for_platform:")
    platforms = ["Spotify", "Facebook", "Website"]
    for platform in platforms:
        metrics = platform_metrics.get_metrics_for_platform(platform)
        print(f"  {platform}:")
        print(f"    Primary: {metrics['primary']}")
        print(f"    Secondary: {metrics['secondary']}")
        print(f"    Calculated: {metrics['calculated']}")

    # Test getting metrics for category
    print("\n3. Testing get_metrics_for_category:")
    categories = ["podcast", "social", "web"]
    for category in categories:
        metrics = platform_metrics.get_metrics_for_category(category)
        print(f"  {category}:")
        print(f"    Primary: {metrics['primary']}")
        print(f"    Secondary: {metrics['secondary']}")
        print(f"    Calculated: {metrics['calculated']}")

    # Test getting all metrics for platform
    print("\n4. Testing get_all_metrics_for_platform:")
    platforms = ["Spotify", "Facebook", "Website"]
    for platform in platforms:
        metrics = platform_metrics.get_all_metrics_for_platform(platform)
        print(f"  {platform}: {metrics}")

    # Test getting display name
    print("\n5. Testing get_display_name:")
    metrics = ["plays", "unique_visitors", "engagement_rate"]
    for metric in metrics:
        display_name = platform_metrics.get_display_name(metric)
        print(f"  {metric}: {display_name}")

    # Test getting description
    print("\n6. Testing get_description:")
    metrics = ["plays", "unique_visitors", "engagement_rate"]
    for metric in metrics:
        description = platform_metrics.get_description(metric)
        print(f"  {metric}: {description}")

    # Test calculating metrics
    print("\n7. Testing calculate_metric:")
    test_data = {
        "engagements": 100,
        "impressions": 1000,
        "plays": 80,
        "starts": 100,
        "video_views": 500,
        "watch_time": 15000
    }
    calculated_metrics = ["engagement_rate", "completion_rate", "avg_watch_time"]
    for metric in calculated_metrics:
        value = platform_metrics.calculate_metric(metric, test_data)
        print(f"  {metric}: {value}")

    # Test getting primary metric
    print("\n8. Testing get_primary_metric_for_platform:")
    platforms = ["Spotify", "Facebook", "URL"]
    for platform in platforms:
        primary_metric = platform_metrics.get_primary_metric_for_platform(platform)
        print(f"  {platform}: {primary_metric}")

    # Test podcast types
    print("\n9. Testing podcast types:")
    print(f"  Number of podcast types: {len(platform_metrics.get_podcast_types())}")
    test_terms = ["Gen Z", "Green Deal", "random"]
    for term in test_terms:
        is_type = platform_metrics.is_podcast_type(term)
        print(f"  Is '{term}' a podcast type? {is_type}")

    # Test podcast type categories
    print("\n10. Testing podcast type categories:")
    test_types = ["Gen Z", "GEN Z", "GenZ", "Green Deal", "EUROPE", "random"]
    for pt in test_types:
        category = platform_metrics.get_podcast_type_category(pt)
        normalized = platform_metrics.get_normalized_podcast_type(pt)
        print(f"  '{pt}' -> Category: {category}, Normalized: {normalized}")

    print("\nTests completed.")

if __name__ == "__main__":
    test_platform_metrics()
