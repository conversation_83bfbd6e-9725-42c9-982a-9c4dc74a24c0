# Gemma SQL Generation with QLoRA Fine-tuning

## Overview

This document outlines the implementation of SQL query generation capabilities for the Euranet Dashboard using Google's Gemma model fine-tuned with QLoRA (Quantized Low-Rank Adaptation). This approach enables the model to directly generate SQL queries from natural language questions, potentially replacing or enhancing the current rule-based SQL generation in the QueryProcessor.

## Core Concepts

### QLoRA Fine-tuning

QLoRA is a memory-efficient fine-tuning technique that:
- Quantizes the base model to 4-bit precision
- Adds trainable "adapter" modules (low-rank matrices)
- Only trains these adapter modules while keeping the base model frozen
- Reduces memory requirements by up to 75% compared to full fine-tuning

This makes it practical to fine-tune Gemma models on standard hardware without requiring specialized GPU resources.

### SQL Generation Capabilities

Fine-tuning Gemma for SQL generation enables:
- Direct translation from natural language questions to executable SQL queries
- Handling of complex query patterns that may be difficult with rule-based approaches
- Adaptation to the specific database schema and query patterns of the Euranet Dashboard
- Potential improvements in query accuracy and complexity handling

## Implementation Plan

### 1. Training Data Preparation

#### Data Format
Each training example should include:
- **Input**: Natural language question + database schema information
- **Output**: Corresponding SQL query

#### Data Sources
- Existing query logs from the Euranet Dashboard
- Synthetic examples covering common analytics scenarios
- Edge cases that the current rule-based system struggles with

#### Example Format
```
### Input:
Database schema:
Table: stations
Columns: station_id (INT), name (TEXT), country (TEXT), language (TEXT)

Table: podcasts
Columns: podcast_id (INT), station_id (INT), title (TEXT), category (TEXT)

Table: metrics
Columns: metric_id (INT), podcast_id (INT), date (DATE), plays (INT), listeners (INT), engagement (FLOAT)

Question: Show me the top 5 podcasts by plays in Germany last month

### Response:
SELECT p.title, SUM(m.plays) as total_plays
FROM podcasts p
JOIN stations s ON p.station_id = s.station_id
JOIN metrics m ON p.podcast_id = m.podcast_id
WHERE s.country = 'Germany'
AND m.date >= date('now', 'start of month', '-1 month')
AND m.date < date('now', 'start of month')
GROUP BY p.podcast_id
ORDER BY total_plays DESC
LIMIT 5;
```

### 2. Fine-tuning Process

#### Environment Setup
```python
# Required libraries
!pip install -q transformers==4.37.2
!pip install -q peft==0.7.1
!pip install -q accelerate==0.25.0
!pip install -q bitsandbytes==0.41.3
!pip install -q trl==0.7.4
```

#### Model Configuration
```python
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

# Configure quantization
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_quant_type="nf4",
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
)

# Load base model
model = AutoModelForCausalLM.from_pretrained(
    "google/gemma-3-1b-it",
    quantization_config=bnb_config,
    device_map="auto",
)

# Configure LoRA
lora_config = LoraConfig(
    r=16,                    # Rank of the update matrices
    lora_alpha=32,           # Parameter for scaling
    lora_dropout=0.05,       # Dropout probability for LoRA layers
    bias="none",
    task_type="CAUSAL_LM",   # Task type
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
)

# Prepare model for training
model = prepare_model_for_kbit_training(model)
model = get_peft_model(model, lora_config)
```

#### Training Configuration
```python
from transformers import TrainingArguments

training_args = TrainingArguments(
    output_dir="./gemma-sql-generator",
    num_train_epochs=3,
    per_device_train_batch_size=4,
    gradient_accumulation_steps=2,
    learning_rate=2e-4,
    weight_decay=0.01,
    warmup_ratio=0.03,
    lr_scheduler_type="cosine",
    save_strategy="epoch",
    logging_steps=10,
    fp16=True,
    optim="paged_adamw_8bit",
)
```

### 3. Integration with Euranet Dashboard

#### New GemmaModel Method
Add a new method to the existing `GemmaModel` class:

```python
def generate_sql(self, query, db_schema=None):
    """
    Generate SQL query from natural language question.
    
    Args:
        query (str): The natural language question.
        db_schema (dict, optional): Database schema information.
                                   If None, uses the stored schema.
    
    Returns:
        str: The generated SQL query.
    """
    if not self.initialized:
        return None
        
    # Use stored schema if none provided
    schema = db_schema if db_schema else self.db_schema
    if not schema:
        return None
        
    # Format schema information
    schema_str = self._format_schema_for_prompt(schema)
    
    # Create prompt
    prompt = f"""
    ### Input:
    Database schema:
    {schema_str}
    
    Question: {query}
    
    ### Response:
    """
    
    # Generate SQL
    response = self.generate_response(prompt, max_tokens=512)
    
    # Extract SQL from response
    sql = self._extract_sql_from_response(response)
    
    return sql
```

#### QueryProcessor Update
Modify the QueryProcessor to use the new SQL generation capability:

```python
def process_query(self, query):
    # ...existing code...
    
    # Use Gemma for SQL generation if available
    if self.use_gemma and self.gemma_model:
        try:
            # Generate SQL using fine-tuned model
            sql = self.gemma_model.generate_sql(query)
            
            # Validate SQL
            if sql and self._validate_sql(sql):
                # Execute the generated SQL
                results = self.db.execute_query(sql)
                return results
        except Exception as e:
            print(f"Error generating SQL with Gemma: {e}")
            # Fall back to rule-based approach
    
    # Fall back to existing rule-based SQL generation
    # ...existing code...
```

### 4. Evaluation Framework

#### Metrics
- **Execution Success Rate**: Percentage of generated SQL queries that execute without errors
- **Result Correctness**: Comparison of results from generated SQL vs. expected results
- **Query Complexity Handling**: Ability to handle joins, aggregations, filters, etc.
- **Performance**: Execution time and resource usage

#### Evaluation Process
1. Create a test set of natural language questions with expected SQL queries
2. Generate SQL for each question using the fine-tuned model
3. Execute both the generated and expected SQL queries
4. Compare the results for correctness
5. Analyze failures to identify improvement areas

## Advantages and Limitations

### Advantages
- More flexible handling of complex queries
- Ability to understand natural language nuances
- Reduced need for manual rule creation
- Potential for handling previously unsupported query types

### Limitations
- Requires high-quality training data
- May generate incorrect SQL for unseen query patterns
- Inference time may be longer than rule-based approaches
- Requires fallback mechanisms for reliability

## Future Improvements

1. **Continuous Fine-tuning**: Regularly update the model with new query patterns
2. **Hybrid Approach**: Combine rule-based and model-based SQL generation
3. **Query Optimization**: Fine-tune to generate not just correct but efficient SQL
4. **Multi-dialect Support**: Extend to support different SQL dialects
5. **Explanation Generation**: Add capability to explain the generated SQL

## References

- [Google Gemma QLoRA Fine-tuning Documentation](https://ai.google.dev/gemma/docs/core/huggingface_text_finetune_qlora)
- [QLoRA Paper: "QLoRA: Efficient Finetuning of Quantized LLMs"](https://arxiv.org/abs/2305.14314)
- [PEFT Library Documentation](https://huggingface.co/docs/peft/index)
```

## Implementation Timeline

| Phase | Duration | Description |
|-------|----------|-------------|
| 1. Data Collection | 2 weeks | Gather and prepare training examples |
| 2. Model Fine-tuning | 1 week | Fine-tune Gemma using QLoRA |
| 3. Integration | 1 week | Integrate with QueryProcessor |
| 4. Testing & Evaluation | 2 weeks | Comprehensive testing and refinement |
| 5. Deployment | 1 week | Deploy to production environment |

## Conclusion

Fine-tuning Gemma for SQL generation represents a significant enhancement to the Euranet Dashboard's natural language query capabilities. By leveraging QLoRA for efficient fine-tuning, we can create a specialized model that directly translates user questions into SQL queries tailored to our specific database schema and analytics needs.