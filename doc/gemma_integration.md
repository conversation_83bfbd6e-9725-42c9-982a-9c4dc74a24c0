# Gemma 3 1B Integration

This document describes the integration of Google's Gemma 3 1B model into the Euranet Dashboard application.

## Overview

Gemma 3 1B is a lightweight local LLM model from Google that can run on devices with limited resources. It has been integrated into the Euranet Dashboard to enhance the natural language understanding and insight generation capabilities of the application. The integration has been optimized for performance, context awareness, and memory efficiency.

## Features

The Gemma integration provides the following features:

1. **Enhanced Query Understanding**: <PERSON> analyzes user queries to better understand intent, entities, and context.
2. **Improved Insight Generation**: Gemma generates more nuanced and contextually relevant insights from data.
3. **Local Processing**: All processing happens locally, ensuring privacy and reducing latency.
4. **Context Awareness**: The model is provided with database schema and metrics information for better understanding.
5. **Response Caching**: Common queries are cached to improve performance and reduce latency.
6. **Memory Efficiency**: Optimized model loading and parameter settings for better memory usage.
7. **Multi-Agent Integration**: Powers specialized agents for trend, comparison, and anomaly detection.

## Implementation Details

### Components

1. **GemmaModel Class**: A wrapper for the Gemma model that handles initialization and inference.
   - Located in `components/gemma_model.py`
   - Directly integrates with the Transformers library for local model usage
   - Also supports Google API as a fallback option
   - Implements response caching for better performance
   - Provides context-aware prompting with database schema and metrics information
   - Optimized model parameters for better memory efficiency

2. **QueryProcessor Integration**: The QueryProcessor has been updated to use Gemma for:
   - Enhanced query classification
   - Improved insight generation
   - Better natural language understanding
   - Context-aware query processing

3. **Chat Callbacks**: The chat callbacks have been updated to display Gemma's enhanced understanding and insights.

4. **Multi-Agent System**: The Gemma model powers a system of specialized agents:
   - TrendAgent: Identifies trends and patterns over time
   - ComparisonAgent: Compares different entities and identifies differences
   - AnomalyAgent: Detects anomalies and outliers in data
   - MultiAgentSystem: Coordinates the specialized agents

### Configuration

The Gemma integration can be configured in the following ways:

1. **Enable/Disable Gemma**: Set the `use_gemma` parameter in the QueryProcessor initialization to `True` or `False`.
2. **API vs Local**: Set the `use_api` parameter in the GemmaModel initialization to `True` or `False`.
3. **API Key**: Set the `api_key` parameter when initializing GemmaModel to use the Google API.
4. **Model Selection**: Change the `model_name` parameter in the GemmaModel initialization to use a different model variant.
5. **Fallback Mechanism**: The system automatically falls back to rule-based responses if the model fails to load or generate responses.
6. **Cache Size**: Set the `cache_size` parameter to control the number of cached responses.
7. **Context Information**: Use `set_db_schema` and `set_metrics_info` methods to provide context information.
8. **Model Parameters**: Adjust generation parameters like `max_tokens`, `top_k`, and `top_p` for different use cases.

## Usage

The Gemma integration is transparent to the end user. When a user enters a query, the application will:

1. Use Gemma to enhance understanding of the query
2. Process the query using the enhanced understanding
3. Generate insights using the multi-agent system powered by Gemma
4. Display the results and insights to the user
5. Store insights in agent memory for future reference

### Authentication

The application is configured to use the Gemma 3-1b-it model directly through the Transformers library. Authentication with HuggingFace has been completed using the following steps:

1. Created a HuggingFace account at https://huggingface.co/
2. Accepted the model license at https://huggingface.co/google/gemma-3-1b-it
3. Generated an access token at https://huggingface.co/settings/tokens
4. Authenticated using `huggingface-cli login`

The application will now use the actual Gemma model for enhanced query understanding and insight generation.

## Requirements

- Python 3.8+
- PyTorch (installed via `pip install torch`)
- Transformers (installed via `pip install transformers`)
- Accelerate (installed via `pip install accelerate`)
- Google GenerativeAI (installed via `pip install google-generativeai`, for API usage)
- 4GB+ RAM for local model inference
- Internet connection for initial model download (one-time requirement)
- HuggingFace account with access to the Gemma model (requires accepting the model license)

## Current Status

The Gemma 3-1b-it model has been successfully integrated and optimized in the application. The model is loaded directly from HuggingFace using the Transformers library and runs locally on the user's machine. The integration includes response caching, context-aware prompting, and memory efficiency optimizations. The model powers a multi-agent system for specialized insight generation with memory tracking. The application includes a cascading fallback mechanism that ensures functionality even on hardware configurations that may not fully support the model.

## Known Issues

1. **Apple Silicon Compatibility**: On macOS with Apple Silicon (M1/M2/M3), the model is forced to run on CPU instead of the Metal Performance Shaders (MPS) device due to compatibility issues. This may result in slower inference times but ensures stability.
2. **Memory Usage**: The model requires significant memory resources (4GB+), which may cause performance issues on systems with limited RAM.
3. **Initial Loading Time**: The first load of the model can take several minutes as it downloads the model weights (~2GB) from HuggingFace.
4. **Generation Warnings**: There may be some warnings about generation parameters in the console output. These are harmless and don't affect the functionality of the application.

## Future Improvements

1. **Fine-tuning**: Fine-tune the Gemma model on Euranet-specific data to improve performance.
2. **Advanced Caching**: Implement more sophisticated caching strategies like LRU or priority-based caching.
3. **Model Quantization**: Implement quantization to further reduce memory usage and improve inference speed.
4. **Multi-modal Support**: Extend the integration to support image and audio inputs.
5. **Performance Optimization**: Further optimize the model loading and inference for faster response times.
6. **Device Compatibility**: Improve compatibility with different hardware configurations.
7. **Agent Specialization**: Develop more specialized agents for different types of insights.
8. **Memory Enhancement**: Improve the memory system with more sophisticated relevance scoring and retrieval.
9. **Distributed Processing**: Implement distributed processing for better performance on multi-core systems.
