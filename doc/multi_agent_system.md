# Multi-Agent Insight Generation System

This document describes the multi-agent insight generation system implemented in the Euranet Dashboard application.

## Overview

The multi-agent insight generation system is a specialized component that uses multiple AI agents to analyze data and generate insights. Each agent focuses on a specific type of analysis, allowing for more comprehensive and specialized insights. The system also includes memory tracking to provide context-aware insights based on past observations.

## Features

The multi-agent system provides the following features:

1. **Specialized Agents**: Different agents focus on specific types of analysis (trends, comparisons, anomalies).
2. **Memory Tracking**: Agents remember past observations for more contextual insights.
3. **Relevance Scoring**: Memories are retrieved based on relevance to the current query.
4. **Time Decay**: More recent memories are given higher priority.
5. **Context Awareness**: Agents use query context and data statistics to generate insights.
6. **Insight Synthesis**: Multiple agent insights are combined into a coherent narrative.
7. **Fallback Mechanisms**: Cascading fallback system ensures insights are generated even if some components fail.

## Implementation Details

### Components

1. **InsightAgent (Abstract Base Class)**: Defines the interface for all insight agents.
   - Located in `components/insight_agents.py`
   - Provides memory management and relevance scoring
   - Defines the abstract `generate_insights` method

2. **TrendAgent**: Specializes in identifying trends and patterns over time.
   - Analyzes time-series data for long-term trends
   - Detects seasonal patterns and cyclical behavior
   - Identifies acceleration or deceleration in trends

3. **ComparisonAgent**: Specializes in comparing different entities.
   - Identifies significant differences between entities
   - Creates relative performance rankings
   - Detects outliers among entities

4. **AnomalyAgent**: Specializes in detecting anomalies and outliers.
   - Identifies unusual values that deviate from the norm
   - Detects unexpected patterns or relationships
   - Finds data points that don't follow general trends

5. **MultiAgentSystem**: Coordinates the specialized agents.
   - Selects appropriate agents based on query context
   - Manages agent execution and insight collection
   - Synthesizes insights from multiple agents

### Memory System

The memory system is a key component of the multi-agent architecture:

1. **Memory Storage**: Each agent maintains its own memory of past observations.
2. **Memory Item**: A memory item includes:
   - Content: The insight or observation
   - Type: The type of insight (trend, comparison, anomaly)
   - Timestamp: When the observation was made
   - Additional metadata specific to the insight type

3. **Relevance Scoring**: Memories are retrieved based on:
   - Keyword matching with the current query
   - Time decay factor (more recent memories get higher scores)
   - Insight type relevance to the current query

4. **Memory Management**: The system limits memory size to prevent unbounded growth (default: 100 items).

### Agent Selection

The system selects which agents to use based on the query and context:

1. **Keyword Detection**: Identifies keywords related to trends, comparisons, or anomalies.
2. **Query Classification**: Classifies the query based on intent and structure.
3. **Default Behavior**: If no specific agents are selected, all agents are used.

### Insight Generation Process

The insight generation process follows these steps:

1. **Query Analysis**: The query is analyzed to determine which agents to use.
2. **Context Preparation**: Query context and data statistics are prepared.
3. **Agent Execution**: Selected agents generate insights from the data.
4. **Memory Retrieval**: Relevant memories are retrieved to provide context.
5. **Insight Generation**: Each agent generates insights based on data and memories.
6. **Memory Update**: New insights are added to agent memories.
7. **Insight Synthesis**: Insights from multiple agents are synthesized (if using Gemma).
8. **Result Formatting**: Insights are formatted for display.

## Usage

The multi-agent system is integrated into the QueryProcessor and is used automatically when processing queries:

```python
# Initialize the multi-agent system
multi_agent_system = MultiAgentSystem(gemma_model)

# Generate insights
context = {"platform": "social_media", "metric": "engagement"}
insights = multi_agent_system.generate_insights(query, data, context)
```

### Configuration

The multi-agent system can be configured in the following ways:

1. **Agent Selection**: Configure which specialized agents to use for different query types.
2. **Memory Capacity**: Adjust the memory capacity for agents (default: 100 items).
3. **Gemma Integration**: Enable or disable Gemma for enhanced insights and synthesis.
4. **Relevance Scoring**: Adjust the parameters for memory relevance scoring.

## Current Status

The multi-agent system has been successfully implemented and integrated into the Euranet Dashboard. The system generates specialized insights for trends, comparisons, and anomalies, with memory tracking for context awareness. The implementation includes comprehensive testing and fallback mechanisms to ensure robustness.

## Known Issues

1. **Memory Limitations**: The memory system has a fixed capacity (100 items) which may limit long-term context.
2. **Redundant Insights**: In some cases, different agents may generate similar insights.
3. **Performance Impact**: Using multiple agents may increase processing time for complex queries.
4. **Memory Relevance**: The current keyword-based relevance scoring is relatively simple and may miss semantic relationships.

## Future Improvements

1. **Advanced Agent Specialization**: Develop more specialized agents for different types of insights.
2. **Enhanced Memory System**: Implement more sophisticated memory retrieval and relevance scoring.
3. **Collaborative Agents**: Enable agents to collaborate and share insights for more comprehensive analysis.
4. **User Feedback Loop**: Allow users to provide feedback on insights to improve agent performance.
5. **Personalized Insights**: Generate insights tailored to user preferences and past interactions.
6. **Distributed Processing**: Implement parallel processing for better performance with multiple agents.
7. **Semantic Memory**: Implement semantic matching for better memory retrieval.
8. **Hierarchical Agents**: Create a hierarchy of agents with different levels of specialization.
9. **Active Learning**: Implement active learning to improve agent performance over time.
10. **Cross-Query Context**: Maintain context across multiple queries for more coherent analysis.
