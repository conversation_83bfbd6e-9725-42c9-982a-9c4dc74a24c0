# Flexible Metric Calculations

This document describes the flexible metric calculation system implemented in the Euranet Dashboard application.

## Overview

The flexible metric calculation system allows users to select different metric types (Average, Total, Count, Percentage) for data analysis. This replaces the previous hardcoded approach where only one metric type was available. The system includes components for mapping platforms to their associated metrics, calculating different metric types, and detecting the desired metric type from natural language queries.

## Features

The flexible metric calculation system provides the following features:

1. **Multiple Metric Types**: Support for different metric types (AVG, SUM, COUNT, PERCENTAGE).
2. **Platform-Metric Mapping**: Comprehensive mapping of platforms to their associated metrics.
3. **Metric Type Detection**: Detection of the desired metric type from natural language queries.
4. **Dynamic SQL Generation**: Generation of SQL queries based on the selected metric type.
5. **UI Integration**: Integration with the UI through a metric selector dropdown.
6. **Podcast Type Support**: Special handling for podcast types with normalization and categorization.

## Implementation Details

### Components

1. **PlatformMetrics**: Manages the mapping between platforms and metrics.
   - Located in `components/platform_metrics.py`
   - Provides methods for getting metrics for specific platforms
   - Handles podcast type normalization and categorization
   - Maintains a comprehensive mapping of platforms to metrics

2. **MetricCalculator**: Handles flexible metric calculations.
   - Located in `components/metric_calculator.py`
   - Supports different metric types (AVG, SUM, COUNT, PERCENTAGE)
   - Detects metric types from natural language queries
   - Generates SQL fragments for different metric types
   - Formats metric values for display

3. **MetricSelector**: Provides a UI component for selecting metric types.
   - Located in `components/metric_selector.py`
   - Creates a dropdown for selecting different metric types
   - Includes tooltips with metric descriptions
   - Updates the UI when a new metric type is selected

### Platform-Metric Mapping

The platform-metric mapping is a key component of the system:

1. **Platform Categories**:
   - Social Media: Facebook, Instagram, Twitter, LinkedIn, etc.
   - Podcast: Spotify, Apple Podcasts, Google Podcasts, etc.
   - URL: Website metrics

2. **Metric Types by Platform**:
   - Social Media: reach, impressions, engagements, likes, shares, etc.
   - Podcast: plays, listeners, followers, subscribers, etc.
   - URL: unique_visitors, visits, page_views

3. **Podcast Types**:
   - Retrieved directly from the database
   - Normalized to handle variations in naming
   - Categorized for easier filtering

### Metric Types

The system supports the following metric types:

1. **AVG (Average)**: Calculates the average value of a metric.
   - SQL: `AVG(metric_column)`
   - Use case: Understanding typical performance

2. **SUM (Total)**: Calculates the total sum of a metric.
   - SQL: `SUM(metric_column)`
   - Use case: Understanding overall volume

3. **COUNT (Count)**: Counts the number of entries.
   - SQL: `COUNT(*)`
   - Use case: Understanding frequency or occurrence

4. **PERCENTAGE (Percentage)**: Calculates a percentage relative to a total.
   - SQL: `(metric_column / total) * 100`
   - Use case: Understanding relative performance

### Metric Type Detection

The system detects the desired metric type from natural language queries:

1. **Keyword Matching**: Identifies keywords related to different metric types.
   - AVG: "average", "mean", "typical", etc.
   - SUM: "total", "sum", "overall", etc.
   - COUNT: "count", "number", "how many", etc.
   - PERCENTAGE: "percent", "proportion", "share", etc.

2. **Default Behavior**: If no specific metric type is detected, the system uses the default (AVG).

3. **ADK Integration**: The ADK integration enhances metric type detection with more sophisticated natural language understanding.

### SQL Generation

The system generates SQL queries based on the selected metric type:

1. **SQL Templates**: Each metric type has a corresponding SQL template.
   - AVG: `SELECT AVG(metric_column) FROM table WHERE conditions`
   - SUM: `SELECT SUM(metric_column) FROM table WHERE conditions`
   - COUNT: `SELECT COUNT(*) FROM table WHERE conditions`
   - PERCENTAGE: `SELECT (metric_column / total) * 100 FROM table WHERE conditions`

2. **Dynamic SQL**: The system dynamically generates SQL based on the selected metric type, platform, and other query parameters.

3. **Query Optimization**: The generated SQL is optimized for performance with appropriate indexing and filtering.

## Usage

The flexible metric calculation system is integrated into the QueryProcessor and is used automatically when processing queries:

```python
# Initialize the components
platform_metrics = PlatformMetrics()
metric_calculator = MetricCalculator()

# Get metrics for a platform
metrics = platform_metrics.get_metrics_for_platform("Facebook")

# Detect metric type from a query
metric_type = metric_calculator.detect_metric_type_from_query("Show me the average engagement")

# Generate SQL for a metric type
sql_fragment = metric_calculator.get_sql_for_metric_type("engagement", metric_type)
```

### UI Integration

The system is integrated into the UI through a metric selector dropdown:

1. **Metric Selector Dropdown**: Located in the Key Insights section.
2. **Tooltips**: Provide descriptions of each metric type.
3. **Dynamic Updates**: The UI updates when a new metric type is selected.
4. **Visual Feedback**: The selected metric type is highlighted.

## Current Status

The flexible metric calculation system has been successfully implemented and integrated into the Euranet Dashboard. The system supports multiple metric types, comprehensive platform-metric mapping, and natural language metric type detection. The implementation includes UI integration through a metric selector dropdown and special handling for podcast types.

## Future Improvements

1. **Advanced Metric Calculations**: Implement more advanced metric calculations such as moving averages, growth rates, and custom formulas.
2. **Personalized Metrics**: Allow users to save their preferred metric types and create custom metrics.
3. **Metric Combinations**: Support combining multiple metrics in a single calculation.
4. **Time-Based Metrics**: Implement time-based metrics such as year-over-year growth and month-over-month change.
5. **Comparative Metrics**: Implement metrics that compare performance across different platforms or time periods.
6. **Predictive Metrics**: Implement predictive metrics that forecast future performance.
7. **Anomaly Detection Metrics**: Implement metrics that identify anomalies and outliers.
8. **Custom Metric Formulas**: Allow users to create custom metric formulas using a formula builder.
9. **Metric Visualization Recommendations**: Recommend the best visualization type for each metric.
10. **Metric Documentation**: Provide detailed documentation for each metric type and its use cases.
