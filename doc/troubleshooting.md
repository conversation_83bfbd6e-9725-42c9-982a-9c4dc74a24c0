# Euranet Dashboard Troubleshooting Guide

## Common Issues and Solutions

### Database Connection Issues

If you encounter problems connecting to the database:

1. Verify that the database file exists at the path specified in the application.
2. Check that you have proper read permissions for the file.
3. Try running the application with admin/sudo permissions if needed.

Default database path:
```
../EuranetDB/euranet_data.db
```

### Missing Required Packages

If you get import errors:

1. Make sure you've installed all dependencies with:
   ```
   pip install -r requirements.txt
   ```
2. Verify that you're using Python 3.11 with:
   ```
   python --version
   ```
3. Note: Python 3.12 may have compatibility issues with numpy/pandas

### UI Rendering Issues

If there are problems with the UI layout:

1. Make sure you're using a supported browser (Chrome, Firefox, Safari).
2. Try clearing your browser cache.
3. Check that your display is set to at least 1024x768 resolution.
4. If CSS is not loading properly, check the assets folder structure.

### Chat Interface Issues

If the chat interface is not working properly:

1. Check if Enter key support is working for sending messages.
2. Verify that the chat history is being stored correctly.
3. Check the browser console for any JavaScript errors.
4. Try restarting the application.

### Visualization Issues

If visualizations are not displaying correctly:

1. Check that <PERSON>lot<PERSON> is properly installed.
2. Verify that the data being passed to the visualizer is in the correct format.
3. Check the browser console for any JavaScript errors.
4. If hover effects are not working, ensure that `staticPlot` is set to `False` in the Graph config.
5. For x-label display issues, check the `fig.update_xaxes()` settings in the visualizer.

### Key Insights Section Issues

If the Key Insights section is not displaying correctly:

1. Verify that the section is properly positioned at the bottom of the visualization panel.
2. Check the CSS for proper styling and separation from the data table.
3. Ensure that the insights data is being passed correctly to the component.

## Advanced Troubleshooting

### Examining Database Schema

To view the database schema directly:

```bash
sqlite3 path/to/euranet_data.db
.tables
.schema table_name
```

### Debugging Mode

To run in debug mode for more verbose logging:

```bash
python app.py
```

The application is configured to run in debug mode by default.

### Component Structure Issues

If you're having issues with the component structure:

1. Check that all imports are correct in the app.py file.
2. Verify that the components, layouts, and callbacks directories are in the correct location.
3. Check that the constants.py file has all the required component IDs.

### CSS Issues

If you're having issues with CSS:

1. Check that the main.css file is importing all the required CSS modules.
2. Verify that the CSS files are in the correct location (assets/styles/).
3. Check the browser console for any CSS errors.
4. Try clearing the browser cache.

## Getting Help

If you encounter persistent issues:

1. Check the GitHub issues for similar problems and solutions.
2. Look for related errors in the Dash documentation.
3. Reach out to the project maintainers with detailed error information.

## Running the Application

To run the application:

```bash
# Using the run script
./run_app.sh

# Or manually
source venv_dash/bin/activate
python app.py
```

The application will be available at http://localhost:8052 by default.
