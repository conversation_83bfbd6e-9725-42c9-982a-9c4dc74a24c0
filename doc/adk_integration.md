# ADK Integration for Euranet Dashboard

This document describes the integration of Google's Agent Development Kit (ADK) into the Euranet Dashboard application.

## Overview

Google's Agent Development Kit (ADK) has been integrated into the Euranet Dashboard to enhance the application's functionality in four key areas:

1. **Data Processing Improvements**: Replacing hardcoded metric calculations with flexible ADK function tools that support multiple aggregation methods (AVG, SUM, COUNT, percentage).
2. **UI Enhancements**: Using ADK's MCP tools to create more dynamic UI components, particularly for the chat panel and data insight panel.
3. **LLM Integration**: Optimizing the Gemma 3-1b-it model integration to better understand user queries and detect desired metric types.
4. **Multi-Agent Insight Generation**: Implementing a system of specialized agents with memory tracking for generating more relevant and contextual insights.

## Features

The ADK integration provides the following features:

1. **Flexible Metric Calculations**: Users can now select different metric types (Average, Total, Count, Percentage) for data analysis.
2. **Enhanced Key Insights Section**: The Key Insights section now has a fixed area at the bottom of the visualization panel with improved styling and visual separation.
3. **Improved Enter Key Support**: The chat panel now better supports using the Enter key to send messages.
4. **Metric Type Detection**: The system can now detect the desired metric type from user queries.
5. **Dynamic UI Components**: Chat and insight panels now adapt to content complexity and relationships.
6. **Comprehensive Platform-Metric Mapping**: Accurate mapping of platforms to their associated metrics.
7. **Multi-Agent Insight Generation**: Specialized agents for trend analysis, comparisons, and anomaly detection.
8. **Memory Tracking**: Agents remember past observations for more contextual insights.

## Implementation Details

### Components

1. **ADKIntegration Class**: A wrapper for the ADK functionality that handles metric processing and UI enhancements.
   - Located in `components/adk_integration.py`
   - Provides methods for processing metrics, enhancing UI components, and detecting metric types

2. **QueryProcessor Integration**: The QueryProcessor has been updated to use ADK for:
   - Enhanced metric type detection
   - Flexible metric calculations
   - Improved query understanding
   - Multi-agent insight generation

3. **Visualization Panel**: The visualization panel has been enhanced with:
   - A fixed Key Insights section at the bottom
   - A metric selector dropdown
   - Better visual separation between components
   - Adaptive insights layout based on content complexity

4. **Chat Panel**: The chat panel has been enhanced with:
   - Improved Enter key support for sending messages
   - Better handling of empty input
   - Debounce to prevent multiple submissions

5. **PlatformMetrics Component**: Manages the mapping between platforms and metrics.
   - Located in `components/platform_metrics.py`
   - Provides methods for getting metrics for specific platforms
   - Handles podcast type normalization and categorization

6. **MetricCalculator Component**: Handles flexible metric calculations.
   - Located in `components/metric_calculator.py`
   - Supports different metric types (AVG, SUM, COUNT, PERCENTAGE)
   - Detects metric types from natural language queries

7. **InsightAgents Component**: Implements a multi-agent system for insight generation.
   - Located in `components/insight_agents.py`
   - Includes specialized agents for trends, comparisons, and anomalies
   - Implements memory tracking for context-aware insights

### Configuration

The ADK integration can be configured in the following ways:

1. **Enable/Disable ADK**: Set the `use_adk` parameter in the QueryProcessor initialization to `True` or `False`.
2. **Default Metric Type**: Change the default metric type in the metric selector dropdown.
3. **Metric Options**: Modify the available metric options in the visualization callbacks.
4. **Agent Selection**: Configure which specialized agents to use for different query types.
5. **Memory Capacity**: Adjust the memory capacity for agents (default: 100 items).
6. **Cache Size**: Set the cache size for response caching (default: 100 items).

## Usage

The ADK integration enhances the user experience in several ways:

1. **Metric Selection**: Users can select different metric types from the dropdown in the Key Insights section.
2. **Natural Language Metric Detection**: Users can specify the desired metric type in their query (e.g., "Show me the total plays" vs. "Show me the average plays").
3. **Enhanced Insights**: Insights are now formatted based on the selected metric type and generated by specialized agents.
4. **Improved UI**: The Key Insights section now has better visual separation and styling.
5. **Dynamic Components**: UI components adapt to content complexity and relationships.
6. **Context-Aware Insights**: Insights take into account past observations and query context.
7. **Specialized Analysis**: Different types of insights (trends, comparisons, anomalies) are generated by specialized agents.

## Requirements

- Python 3.11+
- Dash and Dash Bootstrap Components
- Pandas and Plotly
- Google's Agent Development Kit (ADK)

## Current Status

The ADK integration has been successfully implemented in the Euranet Dashboard. The application now supports flexible metric calculations, enhanced UI components, improved natural language understanding, and multi-agent insight generation with memory tracking. The system provides a more dynamic and adaptive user experience with context-aware insights and specialized analysis.

## Future Improvements

1. **Advanced Metric Calculations**: Implement more advanced metric calculations such as moving averages, growth rates, and custom formulas.
2. **Interactive Insights**: Make insights interactive, allowing users to click on them to see more details or related visualizations.
3. **Personalized Metrics**: Allow users to save their preferred metric types and create custom metrics.
4. **Enhanced Natural Language Understanding**: Further improve the system's ability to understand complex metric requests in natural language.
5. **Multi-Metric Visualizations**: Support displaying multiple metrics in a single visualization for comparison.
6. **Advanced Agent Specialization**: Develop more specialized agents for different types of insights.
7. **Enhanced Memory System**: Implement more sophisticated memory retrieval and relevance scoring.
8. **Collaborative Agents**: Enable agents to collaborate and share insights for more comprehensive analysis.
9. **User Feedback Loop**: Allow users to provide feedback on insights to improve agent performance.
10. **Personalized Insights**: Generate insights tailored to user preferences and past interactions.
