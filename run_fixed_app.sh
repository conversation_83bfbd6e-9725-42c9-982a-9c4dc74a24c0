#!/bin/bash

# This script runs the Euranet Chatbot application with all UI fixes

# Kill any existing Python processes running the app
pkill -f "python app.py" || true
pkill -f "python" || true

# Activate the virtual environment
source venv_dash/bin/activate

# Clear browser cache (optional)
echo "You may want to clear your browser cache before running the app"
echo "In Chrome: Settings > Privacy and security > Clear browsing data"

# Run the application
echo "Starting the application with all UI fixes on port 8051..."
python app.py
